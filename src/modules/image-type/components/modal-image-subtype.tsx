import { ModalType } from "@/common/configs/app.enum";
import { But<PERSON><PERSON><PERSON>mon } from "@/components/common/button-common";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { useGetListNumberFields } from "@/modules/list/hooks/number-fields/useGetListNumberFields";
import { useGetListRockType } from "@/modules/rock-type/hooks/useGetListRockType";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "antd";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useCreateImageSubType } from "../hooks/useCreateImageSubtype";
import { useDeleteImageSubType } from "../hooks/useDeleteImageSubType";
import { useUpdateImageSubType } from "../hooks/useUpdateImageSubType";
import {
  ImageSubTypeBody,
  ImageTypeBodyType,
  ImageSubTypeBodyType,
} from "../model/schema/image-type.schema";

export interface IModalImageSubTypeProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  imageTypeId: string;
  setModalState: (value: any) => void;
  refresh: () => void;
}

export function ModalImageSubType(props: IModalImageSubTypeProps) {
  const { modalState, setModalState, refresh, imageTypeId } = props;
  const {
    request: requestCreateImageSubType,
    loading: loadingCreateImageSubType,
  } = useCreateImageSubType();
  const {
    request: requestUpdateImageSubType,
    loading: loadingUpdateImageSubType,
  } = useUpdateImageSubType();
  const {
    request: requestDeleteImageSubType,
    loading: loadingDeleteImageSubType,
  } = useDeleteImageSubType();
  const { control, handleSubmit } = useForm<ImageSubTypeBodyType>({
    resolver: zodResolver(ImageSubTypeBody),
    defaultValues: {
      ...modalState?.detailInfo,
      isActive: modalState?.detailInfo?.isActive ?? true,
      imageTypeId: imageTypeId,
    },
  });
  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  const isConfirm = modalState.type === ModalType.DELETE;
  const onSubmit = (values: ImageSubTypeBodyType) => {
    if (modalState.type === ModalType.CREATE) {
      requestCreateImageSubType(
        values,
        (res) => {
          toast.success("Create a image sub type successfully");
          setModalState({ ...modalState, isOpen: false });
          refresh();
        },
        (err) => {
          toast.error(err?.message);
        }
      );
    }
    if (modalState.type === ModalType.UPDATE) {
      requestUpdateImageSubType(
        {
          ...values,
          id: modalState.detailInfo.id,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update image sub type successfully");
          setModalState({ ...modalState, isOpen: false });
          refresh();
        },
        (err) => {
          toast.error(err?.message);
        }
      );
    }
  };

  const handleDelete = () => {
    requestDeleteImageSubType(
      {
        id: modalState.detailInfo.id,
      },
      () => {
        setModalState({ ...modalState, isOpen: false });
        refresh();
      },
      (err) => {
        console.log(err);
        toast.error(err?.message);
      }
    );
  };
  const { data: numberFields, request: requestNumberFields } =
    useGetListNumberFields();
  const [keywordNumberField, setKeywordNumberField] = useState("");
  useEffect(() => {
    requestNumberFields({
      maxResultCount: 1000,
      keyword: keywordNumberField,
    });
  }, [keywordNumberField]);

  const { data: rockTypes, request: requestRockTypes } = useGetListRockType();
  const [keywordRockType, setKeywordRockType] = useState("");
  useEffect(() => {
    requestRockTypes({
      maxResultCount: 1000,
      keyword: keywordRockType,
      isActive: true,
    });
  }, [keywordRockType]);
  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={500}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this image sub type?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the image
            sub type
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loadingDeleteImageSubType}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : (
        <div className="px-6 flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === ModalType.UPDATE
              ? "Update image sub type"
              : "Add image sub type"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit)}
            className="flex flex-col gap-3"
          >
            <InputTextCommon
              label="Name"
              name="name"
              placeholder="Type image sub type name here"
              control={control}
            />

            <div className="flex gap-2 flex-wrap">
              <ToogleCommon label="Wet" name="isWet" control={control} />
              <ToogleCommon label="Dry" name="isDry" control={control} />
              <ToogleCommon label="UV" name="isUv" control={control} />
              <ToogleCommon label="Active" name="isActive" control={control} />
            </div>

            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                type="submit"
                loading={loadingCreateImageSubType || loadingUpdateImageSubType}
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === ModalType.UPDATE
                  ? "Update image sub type"
                  : "Add image sub type"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}
