"use client";
import { RequestState } from "@/common/configs/app.contants";
import { useAntdPagination } from "@/common/hooks/useAntdPagination";
import { IPaginationResponse } from "@/common/interfaces/response/IPaginationResponse";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { TableCommon } from "@/components/common/table-common";
import { DeleteOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";
import { IconSearch } from "@tabler/icons-react";
import { Tag, type TableColumnsType } from "antd";
import { debounce } from "lodash";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useCallback, useEffect, useState } from "react";
import { getListImageType } from "../redux/thunks";
import { ModalImageType } from "./modal-image-type";
import Link from "next/link";

const TableImageType: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  //get all query params
  let querySearch: any;
  searchParams.forEach((value, key) => {
    querySearch = {
      ...querySearch,
      [key]: value,
    };
  });
  const imageType = useAppSelector((state) => state.imageType);

  const { handleTableChange, tablePagination, refresh, queries } =
    useAntdPagination({
      reduxTableData: imageType?.result?.items ?? [],
      reduxTablePagination: {
        total: imageType?.result?.pagination.total ?? 0,
      } as IPaginationResponse,
      requestState: imageType?.status ?? RequestState.idle,
      getDataAction: getListImageType,
      pageSize: 50,
    });

  //STATE
  const [modalState, setModalState] = useState<any>({
    isOpen: false,
    type: "create",
  });
  //table
  const columns: TableColumnsType<any> = [
    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      width: 100,
      render: (_, record, index) => {
        return (
          <div className="flex gap-3" key={index}>
            <EditOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "update",
                  detailInfo: record,
                })
              }
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
            />
            <DeleteOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "delete",
                  detailInfo: record,
                })
              }
              className="hover:text-primary cursor-pointer"
              style={{ fontSize: 16 }}
            />
          </div>
        );
      },
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render(value, record, index) {
        return (
          <Link
            key={index}
            href={`/image-type/${record.id}?imageTypeId=${record.id}`}
          >
            {value}
          </Link>
        );
      },
    },
    {
      title: "Active",
      dataIndex: "isActive",
      key: "isActive",
      render(value, index) {
        return (
          <Tag key={index} color={value ? "green" : "red"}>
            {value ? "Active" : "Inactive"}
          </Tag>
        );
      },
    },
    {
      title: "Standard",
      dataIndex: "isStandard",
      key: "isStandard",
      render(value, index) {
        return (
          <Tag key={index} color={value ? "green" : "red"}>
            {value ? "Yes" : "No"}
          </Tag>
        );
      },
    },
    {
      title: "Rig Corrected",
      dataIndex: "isRigCorrected",
      key: "isRigCorrected",
      render(value, index) {
        return (
          <Tag key={index} color={value ? "green" : "red"}>
            {value ? "Yes" : "No"}
          </Tag>
        );
      },
    },
    {
      title: "Rig",
      dataIndex: "isRig",
      key: "isRig",
      render(value, index) {
        return (
          <Tag key={index} color={value ? "green" : "red"}>
            {value ? "Yes" : "No"}
          </Tag>
        );
      },
    },
    {
      title: "Priority",
      dataIndex: "priority",
      key: "priority",
      width: 100,
    },
    // {
    //   title: "Sequence",
    //   dataIndex: "sequence",
    //   key: "sequence",
    //   width: 100,
    // },
    {
      title: "Project",
      dataIndex: "projects",
      key: "projects",
      render(value, index) {
        return value.map((project: any) => project.name).join(", ");
      },
    },
  ];

  const updateSearchParams = useCallback(
    debounce((keyword) => {
      const params = new URLSearchParams(queries);
      keyword ? params.set("keyword", keyword) : params.delete("keyword");
      params.set("page", "1");
      router.replace(`${window.location.pathname}?${params.toString()}`);
    }, 300),
    [queries, router]
  );

  useEffect(() => {
    if (imageType.result?.items.length === 0) {
      if (tablePagination.current && tablePagination.current - 1 > 0) {
        refresh({
          page: tablePagination.current - 1,
        });
        const params = new URLSearchParams(queries);
        params.set("page", (tablePagination.current - 1).toString());
        router.replace(`${window.location.pathname}?${params.toString()}`);
      }
    }
  }, [imageType.result?.items]);
  const buttons = [
    {
      title: "Active",
      isActveString: "true",
      onclick: () => {
        const params = new URLSearchParams(searchParams);
        params.set("isActive", "true");
        params.set("page", "1");
        params.delete("keyword");
        router.replace(`${window.location.pathname}?${params.toString()}`);
      },
    },
    {
      title: "Inactive",
      isActveString: "false",
      onclick: () => {
        const params = new URLSearchParams(searchParams);
        params.set("isActive", "false");
        params.set("page", "1");
        params.delete("keyword");
        router.replace(`${window.location.pathname}?${params.toString()}`);
      },
    },
    {
      title: "All",
      isActveString: undefined,
      onclick: () => {
        const params = new URLSearchParams(searchParams);
        params.delete("isActive");
        params.delete("Keyword");
        params.delete("skipCount");
        router.replace(`${window.location.pathname}?${params.toString()}`);
      },
    },
  ];
  return (
    <>
      {modalState.isOpen && (
        <ModalImageType
          refresh={refresh}
          modalState={modalState}
          setModalState={setModalState}
        />
      )}
      <div className="flex flex-col gap-5">
        <p className="text-34-34 font-semibold">Image Types</p>
        <hr />
        <div className="flex justify-between">
          <div className="px-5 py-2 rounded-lg flex items-center gap-2 h-[38px bg-white border">
            <IconSearch />
            <input
              type="text"
              placeholder="Search"
              className="w-full font-normal outline-none text-primary placeholder:text-gray80"
              onChange={(e) => {
                updateSearchParams(e.target.value);
              }}
              defaultValue={queries.keyword}
            />
          </div>
          <div className="flex gap-2">
            {buttons.map((button, index) => {
              let className: string = "";
              const isActiveSearchParam = searchParams.get("isActive");
              if (isActiveSearchParam === null && button.title === "All") {
                className = "btn-primary btn-active";
              }
              if (isActiveSearchParam === button.isActveString) {
                className = "btn-primary btn-active";
              }
              return (
                <button
                  key={index}
                  className={`btn btn-sm ${className}`}
                  onClick={button.onclick}
                >
                  {button.title}
                </button>
              );
            })}
          </div>
        </div>
        <TableCommon
          className="font-visby"
          rowKey={(record) => (record as any).id}
          pagination={tablePagination}
          loading={imageType.status === RequestState.pending}
          onChange={handleTableChange}
          columns={columns as any}
          dataSource={imageType.result?.items}
          scroll={{ x: "max-content", y: 6 * 100 }}
          footer={() => (
            <div className="justify-center my-2 ">
              <button
                onClick={() => {
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "create",
                    detailInfo: undefined,
                  });
                }}
                className="btn w-full bg-primary border-none hover:bg-primary-hover"
              >
                <PlusOutlined style={{ fontSize: "18px", color: "white" }} />
                <span className="font-bold uppercase text-white ">
                  Add image type
                </span>
              </button>
            </div>
          )}
        />
      </div>
    </>
  );
};

export default TableImageType;
