"use client";
import useEffectAfterMounted from "@/common/hooks/useSkipFirstRender";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { ButtonCommon } from "@/components/common/button-common";
import { InputNumberCommon } from "@/components/common/input-number";
import { useGetListDrillhole } from "@/modules/drillhole/hooks/useGetListDrillHole.hook";
import { useQueryImageSubType } from "@/modules/image-type/hooks/useGetQueryImageSubType";
import { useQueryImageType } from "@/modules/image-type/hooks/useQueryImageType";
import imageRequest from "@/modules/image/api/image.api";
import { ImageStatus } from "@/modules/image/constants/image.constant";
import { imageCategoryOptions } from "@/modules/image/model/enum/images.enum";
import { Form, Select } from "antd";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
interface Props {
  queryFilter?: any;
  setQueryFilter?: any;
  refetchDrillhole?: any;
  setSelectedDrillholeName?: (value: string) => void;
}

function ProcessImagePanel({
  queryFilter,
  setQueryFilter,
  refetchDrillhole,
  setSelectedDrillholeName,
}: Props) {
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );
  const globalProspectId = useAppSelector(
    (state) => state.user.userInfo?.prospectId
  );
  const [maxResultCountDrillhole, setMaxResultCountDrillhole] = useState(100);

  const [keywordDrillhole, setKeywordDrillhole] = useState("");

  const { request: requestGetListDrillhole, data: drillholes } =
    useGetListDrillhole();

  useEffectAfterMounted(() => {
    if (globalProjectId) {
      requestGetListDrillhole({
        skipCount: 0,
        maxResultCount: maxResultCountDrillhole,
        projectIds: globalProjectId ? [globalProjectId] : undefined,
        prospectIds: globalProspectId ? [globalProspectId] : undefined,
        keyword: keywordDrillhole,
        drillHoleStatus: queryFilter.drilHoleStatus,
      });
    }
  }, [
    maxResultCountDrillhole,
    keywordDrillhole,
    queryFilter?.drilHoleStatus,
    refetchDrillhole,
    globalProspectId,
    globalProjectId,
  ]);

  const formDepth = useForm<any>({
    defaultValues: {
      depth: queryFilter.depth,
    },
  });

  const onSubmitDepth = async (data: any) => {
    const { depth } = data;
    const response = await imageRequest.getSkipCountByDepth({
      ...queryFilter,
      projectIds: [globalProjectId],
      prospectIds: [globalProspectId],
      holeIds: [queryFilter.drillholeId],
      depth: depth,
    });

    const skipCount = response?.data;
    setQueryFilter({
      ...queryFilter,
      skipCount: skipCount - 1,
    });
  };

  useEffect(() => {
    setQueryFilter({
      ...queryFilter,
      projectId: globalProjectId,
      prospectId: globalProspectId,
      drillholeId: queryFilter.drillholeId || undefined,
      imageCategory: queryFilter.imageCategory || 1,
    });
  }, [globalProjectId, globalProspectId]);
  const {
    data: imageTypes,
    setSearchParams: setSearchParamsImageType,
    searchParams: imageTypeSearchParams,
  } = useQueryImageType();
  useEffect(() => {
    if (globalProjectId) {
      setSearchParamsImageType((prev) => ({
        ...prev,
        projectId: globalProjectId,
      }));
    }
  }, [globalProjectId, setSearchParamsImageType]);
  const imageTypeOptions = useMemo(() => {
    return imageTypes?.data?.items.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  }, [imageTypes?.data?.items]);

  const { data: imageSubTypes, setSearchParams: setSearchParamsImageSubType } =
    useQueryImageSubType();
  const imageTypeId = queryFilter?.imageTypeId;

  const imageSubTypeOptions = useMemo(() => {
    if (imageTypeId) {
      return (
        imageSubTypes?.data?.items.map((item) => ({
          label: item.name,
          value: item.id,
        })) || []
      );
    }
    return [];
  }, [imageSubTypes?.data?.items, imageTypeId]);
  useEffect(() => {
    if (imageTypeId) {
      setSearchParamsImageSubType({
        imageTypeId: imageTypeId,
      });
    }
  }, [imageTypeId, setSearchParamsImageSubType]);
  return (
    <div className={` pr-3 relative h-full border-r border-gray-300 `}>
      <div className="mt-3">
        <div className="font-medium mb-2 flex justify-between items-center bg-primary px-2 py-1 text-white">
          <div className="flex items-center">
            <span className="mr-1">Drillhole</span>
          </div>
        </div>
        <div className="max-h-full overflow-auto">
          <Select
            className="w-full"
            disabled={!queryFilter?.projectId && !queryFilter?.prospectId}
            showSearch
            filterOption={false}
            onSearch={(value) => {
              setKeywordDrillhole(value);
            }}
            value={queryFilter?.drillholeId}
            options={drillholes?.map((drillhole: any) => ({
              label: drillhole.name,
              value: drillhole.id,
            }))}
            placeholder="Select drillhole"
            onChange={(value) => {
              setSelectedDrillholeName &&
                setSelectedDrillholeName(
                  drillholes.find((item: any) => item.id === value)?.name
                );
              setQueryFilter({
                ...queryFilter,
                drillholeId: value,
                skipCount: 0,
              });
            }}
          />
        </div>
      </div>

      <div className="mt-3">
        <div className="font-medium mb-2 flex justify-between items-center bg-primary px-2 py-1 text-white">
          <div className="flex items-center">Image type</div>
        </div>
        <Select
          className="w-full h-full"
          onClear={() => {
            setQueryFilter({
              ...queryFilter,
              imageTypeId: undefined,
              imageSubTypeId: undefined,
            });
          }}
          onChange={(imageTypeId) => {
            if (!imageTypeId) return;
            setQueryFilter({
              ...queryFilter,
              imageTypeId: imageTypeId,
              imageSubTypeId: undefined,
            });
          }}
          value={queryFilter?.imageTypeId}
          options={imageTypeOptions}
          placeholder="Select image type"
          allowClear
        />
      </div>
      <div className="mt-3">
        <div className="font-medium mb-2 flex justify-between items-center bg-primary px-2 py-1 text-white">
          <div className="flex items-center">Image Subtype</div>
        </div>
        <Select
          className="w-full h-full"
          onClear={() => {
            setQueryFilter({
              ...queryFilter,
              imageSubTypeId: undefined,
            });
          }}
          onChange={(e) => {
            if (!e) return;
            setQueryFilter({
              ...queryFilter,
              imageSubTypeId: e,
            });
          }}
          value={queryFilter?.imageSubTypeId}
          options={imageSubTypeOptions}
          placeholder="Select image subtype"
          allowClear
        />
      </div>

      <div className="mt-3">
        <div className="font-medium mb-2 flex justify-between items-center bg-primary px-2 py-1 text-white">
          <div className="flex items-center">Image Category</div>
        </div>
        <Select
          className="w-full h-full"
          onClear={() => {
            setQueryFilter({
              ...queryFilter,
              imageCategory: undefined,
            });
          }}
          onChange={(e) => {
            if (!e) return;
            setQueryFilter({
              ...queryFilter,
              imageCategory: e,
            });
          }}
          value={queryFilter?.imageCategory}
          options={imageCategoryOptions.map((item) => ({
            label: item.label,
            value: item.value,
          }))}
          placeholder="Select Image Category"
          allowClear
        />
      </div>

      <div className="mt-3">
        <div className="font-medium mb-2 flex justify-between items-center bg-primary px-2 py-1 text-white">
          <div className="flex items-center">Depth</div>
        </div>
        <Form
          onFinish={formDepth.handleSubmit(onSubmitDepth)}
          className="flex items-center gap-2"
        >
          <InputNumberCommon
            className="w-full h-full"
            name="depth"
            control={formDepth.control}
            placeholder="Enter depth"
          />
          <div className="flex flex-col gap-2">
            <ButtonCommon type="submit" className="btn btn-primary btn-sm">
              Apply
            </ButtonCommon>
            <ButtonCommon
              type="button"
              className="btn btn-error btn-sm text-white"
              onClick={() => {
                formDepth.reset();
                setQueryFilter({
                  ...queryFilter,
                  depth: undefined,
                });
              }}
            >
              Clear
            </ButtonCommon>
          </div>
        </Form>
      </div>
      <div className="mt-3">
        <div className="font-medium mb-2 flex justify-between items-center bg-primary px-2 py-1 text-white">
          <div className="flex items-center">Image Status</div>
        </div>
        <Select
          className="w-full h-full"
          onClear={() => {
            setQueryFilter({
              ...queryFilter,
              imageStatus: undefined,
            });
          }}
          onChange={(e) => {
            if (!e) return;
            setQueryFilter({
              ...queryFilter,
              imageStatus: e,
            });
          }}
          value={queryFilter?.imageStatus}
          options={ImageStatus.map((item) => ({
            label: item.label,
            value: item.value,
          }))}
          placeholder="Select Image Status"
          allowClear
        />
      </div>
    </div>
  );
}

export default ProcessImagePanel;
