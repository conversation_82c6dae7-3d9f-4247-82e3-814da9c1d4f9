import { Mo<PERSON><PERSON><PERSON>mon } from "@/components/common/modal-common";
import { TableCommon } from "@/components/common/table-common";
import { useGetListRockType } from "@/modules/rock-type/hooks/useGetListRockType";
import { IconSearch } from "@tabler/icons-react";
import { Tag } from "antd";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import rockGroupsRequest from "../api/rock-groups.api";
import { useGetRockGroup } from "../hooks/useGetRockGroups";

export default function ModalAddRockType({
  isOpen,
  id,
  setIsOpen,
  refresh,
}: {
  isOpen: boolean;
  id: string;
  setIsOpen: (isOpen: boolean) => void;
  refresh: () => void;
}) {
  const { request: requestGetRockGroup, loading: loadingGetRockGroup } =
    useGetRockGroup();

  const [keywordRockType, setKeywordRockType] = useState("");
  const [selectedRockType, setSelectedRockType] = useState<any[]>([]);
  const [pageRockType, setPageRockType] = useState(1);

  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      setSelectedRockType(selectedRowKeys);
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.name === "Disabled User", // Column configuration not to be checked
    }),
    selectedRowKeys: selectedRockType,
    preserveSelectedRowKeys: true,
  };
  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      width: 200,
    },
    {
      title: "Description",
      dataIndex: "description",
    },
    {
      title: "Code  ",
      dataIndex: "code",
    },
    {
      width: 200,
      title: "Is Active",
      dataIndex: "isActive",
      render: (isActive: boolean) =>
        isActive ? (
          <Tag color="success">Active</Tag>
        ) : (
          <Tag color="error">Inactive</Tag>
        ),
    },
  ];
  useEffect(() => {
    requestRockTypes({
      keyword: keywordRockType,
      maxResultCount: 50,
      skipCount: (pageRockType - 1) * 50,
      isActive: true,
    });
  }, [keywordRockType, pageRockType]);
  const {
    data: rockTypes,
    request: requestRockTypes,
    loading: loadingRockTypes,
    total: totalRockTypes,
  } = useGetListRockType();
  useEffect(() => {
    if (id) {
      requestGetRockGroup(id, (res) => {
        const rockTypeIds = res?.rockTypes?.map((item) => item.id);
        setSelectedRockType(rockTypeIds);
      });
    }
  }, [id]);
  const [loading, setLoading] = useState(false);
  return (
    <ModalCommon
      title="Add Rock Type"
      width={800}
      open={isOpen}
      onCancel={() => setIsOpen(false)}
      okText="Save change"
      confirmLoading={loading}
      onOk={() => {
        setLoading(true);
        rockGroupsRequest
          .assignRockType({
            rockGroupId: id as any,
            rockTypeIds: selectedRockType,
          })
          .then(() => {
            toast.success("Add rock type success");
            setIsOpen(false);
            refresh();
          })
          .catch((e) => {
            toast.error(e.message);
          })
          .finally(() => {
            setLoading(false);
          });
      }}
    >
      <div className="flex flex-col gap-3">
        <div className="px-5 py-2 rounded-lg flex items-center gap-2 h-[38px bg-white border">
          <IconSearch />
          <input
            type="text"
            placeholder="Search"
            className="w-full font-normal outline-none text-primary placeholder:text-gray80"
            onChange={(e) => {
              setKeywordRockType(e.target.value);
            }}
          />
        </div>
        <TableCommon
          rowSelection={rowSelection}
          scroll={{ x: "max-content", y: 6 * 70 }}
          columns={columns}
          dataSource={rockTypes}
          loading={loadingRockTypes}
          pagination={{
            total: totalRockTypes,
            pageSize: 50,
            current: pageRockType,
            onChange: (page) => {
              setPageRockType(page);
            },
          }}
          rowKey={(record) => (record as any).id}
        />
      </div>
    </ModalCommon>
  );
}
