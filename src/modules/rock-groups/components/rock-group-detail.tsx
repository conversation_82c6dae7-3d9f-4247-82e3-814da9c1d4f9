"use client";
import { TableCommon } from "@/components/common/table-common";
import { ModalRockType } from "@/modules/rock-type/components/modal-rock-type";
import { DeleteOutlined, EditOutlined } from "@ant-design/icons";
import { IconSearch } from "@tabler/icons-react";
import { Button, Tag } from "antd";
import { useEffect, useState } from "react";
import { useGetRockGroup } from "../hooks/useGetRockGroups";
import ModalAddRockType from "./modal-add-rock-type";
import { useGetListRockType } from "@/modules/rock-type/hooks/useGetListRockType";
import { toast } from "react-toastify";
import rockGroupsRequest from "../api/rock-groups.api";
export default function RockGroupDetail({ id }: { id?: string }) {
  const {
    request: requestGetRockGroup,
    loading: loadingGetRockGroup,
    data: rockGroup,
  } = useGetRockGroup();
  const [keywordRockType, setKeywordRockType] = useState("");
  const refresh = () => {
    if (id) {
      requestGetRockGroup(id);
    }
  };
  useEffect(() => {
    refresh();
  }, [id]);

  //   const {
  //     data: rockTypes,
  //     request: requestRockTypes,
  //     loading: loadingRockTypes,
  //     total: totalRockTypes,
  //   } = useGetListRockType();
  //   const [keywordRockType, setKeywordRockType] = useState("");
  //   const [pageRockType, setPageRockType] = useState(1);

  //   useEffect(() => {
  //     requestRockTypes({
  //       keyword: keywordRockType,
  //       maxResultCount: 50,
  //       skipCount: (pageRockType - 1) * 50,
  //       isActive: true,
  //     });
  //   }, [keywordRockType, pageRockType]);
  //STATE
  const [modalState, setModalState] = useState<any>({
    isOpen: false,
    type: "create",
  });
  const [modalAddRockType, setModalAddRockType] = useState<any>(false);
  const columns = [
    {
      title: "Action",
      dataIndex: "action",
      width: 100,
      key: "action",
      render: (_, record, index) => {
        return (
          <div className="flex gap-3" key={index}>
            <EditOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "update",
                  detailInfo: record,
                })
              }
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
            />
            <DeleteOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "delete",
                  detailInfo: record,
                })
              }
              className="hover:text-primary cursor-pointer"
              style={{ fontSize: 16 }}
            />
          </div>
        );
      },
    },
    {
      title: "Rock Type",
      dataIndex: "name",
      width: 200,
    },
    {
      title: "Fill Color",
      dataIndex: "fillColor",
      width: 200,
      render: (_, record) => {
        return (
          <div
            className="w-full h-4 rounded-full"
            style={{ backgroundColor: record.rockStyle?.fillColor }}
          >
            <div className="w-full h-full rounded-full"></div>
          </div>
        );
      },
    },
    {
      title: "Description",
      dataIndex: "description",
    },
    {
      width: 200,
      title: "Is Active",
      dataIndex: "isActive",
      render: (isActive: boolean) =>
        isActive ? (
          <Tag color="success">Active</Tag>
        ) : (
          <Tag color="error">Inactive</Tag>
        ),
    },
  ];
  const [selectedRockType, setSelectedRockType] = useState<any[]>([]);

  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      setSelectedRockType(selectedRowKeys);
    },

    selectedRowKeys: selectedRockType,
    preserveSelectedRowKeys: true,
  };
  console.log(selectedRockType);
  useEffect(() => {
    if (id) {
      requestGetRockGroup(id, (res) => {
        const rockTypeIds = res?.rockTypes?.map((item) => item.id);
        setSelectedRockType(rockTypeIds);
      });
    }
  }, [id]);
  const [pageRockType, setPageRockType] = useState(1);

  useEffect(() => {
    requestRockTypes({
      keyword: keywordRockType,
      maxResultCount: 50,
      skipCount: (pageRockType - 1) * 50,
      isActive: true,
    });
  }, [keywordRockType, pageRockType]);
  const {
    data: rockTypes,
    request: requestRockTypes,
    loading: loadingRockTypes,
    total: totalRockTypes,
  } = useGetListRockType();
  const [loading, setLoading] = useState(false);
  const handleSaveChanges = () => {
    setLoading(true);
    rockGroupsRequest
      .assignRockType({
        rockGroupId: id as any,
        rockTypeIds: selectedRockType,
      })
      .then(() => {
        toast.success("Add rock type success");
      })
      .catch((e) => {
        toast.error(e.message);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  return (
    <div className="flex flex-col gap-3">
      {modalState.isOpen && (
        <ModalRockType
          refresh={refresh}
          modalState={modalState}
          setModalState={setModalState}
        />
      )}
      {modalAddRockType && (
        <ModalAddRockType
          id={id as any}
          isOpen={modalAddRockType}
          setIsOpen={setModalAddRockType}
          refresh={refresh}
        />
      )}
      <p className="font-bold text-24-28 capitalize text-center font-visby">
        Update Rock Group: {rockGroup?.name}
      </p>
      <div className="px-5 py-2 rounded-lg flex items-center gap-2 h-[38px bg-white border">
        <IconSearch />
        <input
          type="text"
          placeholder="Search"
          className="w-full font-normal outline-none text-primary placeholder:text-gray80"
          onChange={(e) => {
            setKeywordRockType(e.target.value);
          }}
        />
      </div>
      <TableCommon
        rowSelection={rowSelection}
        scroll={{ x: "max-content", y: 6 * 70 }}
        columns={columns}
        dataSource={rockTypes}
        pagination={{
          total: totalRockTypes,
          pageSize: 50,
        }}
        rowKey={(record) => (record as any).id}
        footer={() => (
          <Button
            className="bg-primary text-white hover:bg-primary-hover w-full"
            onClick={handleSaveChanges}
            type="primary"
            loading={loading}
          >
            Save Changes
          </Button>
        )}
      />
    </div>
  );
}
