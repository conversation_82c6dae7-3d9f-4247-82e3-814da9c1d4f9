import z from "zod";

export const ProjectBody = z.object({
  id: z.any().optional(),
  name: z.string().trim().min(1, { message: "Required" }),
  code: z
    .string()
    .trim()
    .min(1, { message: "Required" })
    .max(3, "Code must be a string or array type with a maximum length of 3"),
  description: z.string(),
  backgroundColor: z.string(),
  coreTrayLength: z.number(),
  textColor: z.string().optional(),
  tenantId: z.number().optional(),
  boundingBoxId: z
    .array(z.number())
    .min(1, "At least 1 bounding box is required"),
  boundingRowsId: z
    .array(z.number())
    .min(1, "At least 1 bounding row is required"),
  suiteIds: z.array(z.number()).optional(),
  isActive: z.boolean().optional(),
  mobileWorkflowId: z.number().optional(),
  assaySuiteIds: z.array(z.number()).optional(),
  geologySuiteIds: z.array(z.number()).optional(),
  loggingViewIds: z.array(z.number()).optional(),
  geotechSuiteIds: z.array(z.number()).optional(),
  rqdCalculationIds: z.array(z.number()).optional(),
  imageTypeId: z.number().optional(),
  imageTypeIds: z.array(z.number()).optional(),
  rockGroupId: z.number().optional(),
});

export type ProjectBodyType = z.TypeOf<typeof ProjectBody>;
