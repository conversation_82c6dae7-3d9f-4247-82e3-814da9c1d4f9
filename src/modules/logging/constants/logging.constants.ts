import { EnumLoggingExtraViews } from "../model/enum/logging.enum";
import { RockLineType } from "../types/logging.types";

export const CONFIG = {
  SCALE_BY: 1.1,
  SCALE: 66,
  MIN_SCALE: 0.1,
  MAX_SCALE: 10,
  DEFAULT_PADDING: 65,
  DEFAULT_DISPLAY_COLUMN: {
    fontSize: 12,
    color: "red",
    strokeWidth: 1.5,
    lineColor: "black",
    showText: true,
  },
  TEXT_STYLES: {
    TITLE: {
      fontSize: 16,
      fill: "red",
      fontWeight: 800,
      letterSpacing: 1,
      fontFamily: "Kavivanar",
    },
    DEPTH: {
      fontSize: 12,
      fill: "black",
    },
  },
};

// Logging Image Component Constants
export const VIEW_MODE_OPTIONS = [
  {
    label: "Below",
    value: EnumLoggingExtraViews.Below,
  },
  {
    label: "Overlay",
    value: EnumLoggingExtraViews.Overlay,
  },
];

export const ROCK_LINE_OPTIONS = [
  {
    label: "Recovery",
    value: RockLineType.Recovery,
  },
  {
    label: "RQD",
    value: RockLineType.RQD,
  },
];

// Logging bar configuration constants
export const LOGGING_BAR_HEIGHT = 100;
export const LOGGING_BAR_GAP = 20;
export const DEFAULT_ROCK_GROUP_COLOR = "#CCCCCC";
export const IMAGE_LOGGING_BAR_SPACING = 40;
export const EXTRA_SPACING_FOR_LOGGING_BARS = 40;
