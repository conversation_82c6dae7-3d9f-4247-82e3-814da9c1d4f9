import { NodeConfig } from "konva/lib/Node";
import { EnumLoggingViewStack } from "../enum/logging.enum";

export type ImageProps = NodeConfig;

export type LoggingViewStack<T = any> = {
  id: string;
  type: EnumLoggingViewStack;
  data: T;
  maxWidth?: number;
  minHeight?: number;
  maxHeight?: number;
  startY: number;
  index: number;
  uiProps?: ImageProps;
};

export type LoggingViewData = {
  src: string;
  depthFrom?: number;
  depthTo?: number;
  width?: number;
  height?: number;
  points?: { x: number; y: number }[];
  id: number;
  isShowOCR: boolean;
  isShowText: boolean;
  relativeStartX?: number;
  relativeEndX?: number;
  // Rock group specific properties
  rockGroupName?: string;
  rockTypeName?: string;
  rockTypeCode?: string;
  rockTypeColor?: string;
  x?: number; // X position for rock group rectangles
  // Logging bar specific properties
  color?: string;
  loggingBarId?: number;
  startImageCropId?: number;
  endImageCropId?: number;
  betweenImageCropIds?: number[];
};

export type IntersectionPoints = {
  StartX: number;
  StartY: number;
  EndX: number;
  EndY: number;
};

export type SegmentationResult = {
  name: string;
  isPolyComplete: boolean;
  points: number[][];
  color: string;
  rowIndex: number;
  strokeWidth?: number;
  intersections?: number[][];
  Class: "Block" | "core";
  IntersectionPoints: IntersectionPoints;
};

export type ClickDepthData = {
  relativeX: number;
  depth: number;
};
