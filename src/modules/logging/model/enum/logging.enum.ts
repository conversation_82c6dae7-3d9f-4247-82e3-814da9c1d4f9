export enum EnumLoggingViewStack {
  Image = "Image",
  Below = "Below",
  Overlay = "Overlay",
  Point = "Point",
  RockGroup = "RockGroup",
  LoggingBar = "LoggingBar",
}

export enum EnumLoggingExtraViews {
  Below = "Below",
  Overlay = "Overlay",
}

export enum EnumLoggingViews {
  Image = "Image",
  Grid = "Grid",
}

export enum EnumCalculateClickedDepth {
  Standard = "Standard",
  OcrCalculation = "OcrCalculation",
  ApiCalculation = "ApiCalculation",
}

export enum RockType {
  rockType = 3,
  rockTypeNumber = 6,
  rockTypeSelect = 7,
}
