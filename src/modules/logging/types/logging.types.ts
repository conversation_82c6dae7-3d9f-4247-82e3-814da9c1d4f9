import { FieldType } from "@/modules/geology-suite-field/const/enum";
import { ColumnClass } from "@/modules/logging-view/enum/enum";
import { EnumLoggingExtraViews } from "../model/enum/logging.enum";

export interface DisplayColumnConfig {
  fontSize: number;
  color: string;
  strokeWidth: number;
  lineColor: string;
  showText: boolean;
}

export interface Coordinate {
  height: number;
  startY: number;
  endY: number;
}

export interface GeophysicsPoint {
  x: number;
  y: number;
}

export interface GeologyDataInfo {
  startX: number;
  width: number;
  name: string;
  columnClass: ColumnClass;
  geologySuiteField?: {
    id: string;
    geologyField?: {
      type: FieldType;
    };
  };
}

export interface DataEntryValue {
  fieldType: FieldType;
  rockType?: {
    code?: string;
    rockStyle?: {
      fillColor?: string;
    };
  };
  description?: string;
  colour?: {
    hexCode?: string;
    name?: string;
  };
  dateValue?: string;
  numberValue?: number;
  number?: {
    unit?: {
      code?: string;
    };
  };
  pickListItem?: {
    name?: string;
  };
  rockNode?: {
    name?: string;
  };
  geologysuiteFieldId?: string;
}

export interface ApiError extends Error {
  response?: {
    data?: {
      error?: {
        message?: string;
      };
    };
  };
}

export interface ModalState {
  isOpen: boolean;
  data?: any;
}

export interface Recovery {
  ocrValueFrom: number;
  ocrValueTo: number;
  length: number;
  recovery: number;
  depthInterval: number;
  fromX: number;
  fromY: number;
  fromImageCropId: number;
  fromRowIndex: number;
  toX: number;
  toY: number;
  toImageCropId: number;
  toRowIndex: number;
  id: number;
  fromOcrId: string;
  toOcrId: string;
}

export interface OCRResultItem {
  id?: string | number;
  rowIndex: number;
  text: string | number;
  x?: number;
  y?: number;
  isEdit?: boolean;
  draftText?: string | number;
}

export enum RockLineType {
  Recovery = 1,
  RQD,
}

export interface RockLine {
  type: RockLineType;
  depthFrom: number;
  depthTo: number;
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  imageCropId: number;
  id: number;
}

// Component props interfaces
export interface LoggingImageProps {
  imageRows: any[];
  viewModes: EnumLoggingExtraViews[];
  setViewModes: (modes: EnumLoggingExtraViews[]) => void;
  directOCRdata: OCRResultItem[];
  setDirectOCRdata: (data: OCRResultItem[]) => void;
  image: any;
  directOCRdataRaw: any;
  getCurrentImage: (
    drillholeId: number,
    skipCount: number,
    imageType?: any,
    imageSubtype?: any,
    imageCategory?: any
  ) => void;
  setCurrentImage: (skipCount: number) => void;
  selectedImageType: any;
  selectedImageSubtype: any;
}
