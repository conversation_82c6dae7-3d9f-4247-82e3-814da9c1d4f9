import { StandardType, Type } from "@/modules/image/model/enum/images.enum";
import { ColumnClass } from "@/modules/logging-view/enum/enum";
import {
  CONFIG,
  DEFAULT_ROCK_GROUP_COLOR,
} from "../constants/logging.constants";
import { EnumLoggingViewStack } from "../model/enum/logging.enum";
import { LoggingViewStack } from "../model/dtos/logging.config";
import { Coordinate } from "../types/logging.types";

import { FieldType } from "@/modules/geology-suite-field/const/enum";
import { LoggingRowData } from "../components/logging-grid/table-geology-types";
import { DataEntryValueBody } from "../api/data-entry.api";

export const transformSegmentations = (
  segmentations: any[],
  type: EnumLoggingViewStack,
  imageData: { x: number; y: number; height: number },
  startY: number
): any[] => {
  if (type !== EnumLoggingViewStack.Image) return [];

  return segmentations.map((segmentation) => {
    const points = segmentation.points.map((point) => {
      return [point[0] - imageData.x, startY + point[1] - imageData.y] as [
        number,
        number
      ];
    });

    return {
      ...segmentation,
      points,
    };
  });
};

export const calculateCoordinate = (
  depthFrom: number,
  depthTo: number
): Coordinate => {
  const startY = depthFrom * CONFIG.SCALE;
  const endY = depthTo * CONFIG.SCALE;
  const height = endY - startY;
  return { height, startY, endY };
};

export const calculateStartX = (
  index: number,
  columns: any[],
  gap: number
): number => {
  let startX = 40;
  for (let i = 0; i < index; i++) {
    startX += columns[i].width + gap;
  }
  return startX;
};

export const processLoggingViewData = (
  loggingViewData: any,
  loggingViewColumn: any[],
  gap: number
) => {
  const processedData = loggingViewColumn.map((item) => {
    const fieldType = item?.geologySuiteField?.geologyField?.type;
    let data;
    let coreTypeName;

    switch (item?.columnClass) {
      case ColumnClass.Geology:
        data = (loggingViewData?.geology?.dataEntries ?? [])
          ?.map((dataEntry: any) => ({
            ...dataEntry,
            dataEntryValues: dataEntry.dataEntryValues.filter(
              (dataEntryValue: any) => dataEntryValue.fieldType === fieldType
            ),
          }))
          .filter((dataEntry: any) => {
            return dataEntry?.depthFrom < dataEntry?.depthTo;
          });
        break;
      case ColumnClass.Geophysics:
        data = loggingViewData?.geophysics;
        break;
      case ColumnClass.Assay:
        data = loggingViewData?.assay?.filter((assay: any) => {
          return (
            !isNaN(Number(assay?.["Depth From"])) &&
            !isNaN(Number(assay?.["Depth To"])) &&
            Number(assay?.["Depth From"]) < Number(assay?.["Depth To"])
          );
        });
        break;
      case ColumnClass.CoreRow:
        const filterCoreRow = loggingViewData?.coreRows?.find(
          (coreRow: any) => {
            return (
              coreRow?.imageType?.id === item?.imageType?.id &&
              coreRow?.imageSubType?.id === item?.imageSubType?.id
            );
          }
        );

        const coreRowData: any[] = filterCoreRow?.imageCrops ?? [];

        data = coreRowData
          ?.filter((rowData) => rowData?.type?.toLowerCase() === "row")
          ?.sort((a, b) => a?.depthFrom - b?.depthFrom)
          ?.map((rowData) => {
            const coordinate = JSON.parse(rowData?.coordinate ?? "{}");

            const viewHeight = rowData.depthTo - rowData.depthFrom;
            const viewWidth =
              (coordinate.Height / coordinate.Width) * viewHeight;
            return {
              ...rowData,
              height: viewHeight,
              width: viewWidth,
            };
          });

        break;
    }

    return {
      ...item,
      data,
      coreTypeName,
    };
  });

  return processedData.map((item, index) => ({
    ...item,
    startX: calculateStartX(index, processedData, gap),
  }));
};

// ============================================================================
// TABLE EDITABLE UTILITY FUNCTIONS
// ============================================================================

/**
 * Rounds depth values to 2 decimal places for consistent comparison
 */
export const roundDepth = (value: number): number => {
  return Math.floor(value * 100 + 1e-8) / 100;
};

/**
 * Determines if a field is compound (has multiple inputs)
 */
export const isCompoundField = (
  fieldIndex: number,
  targetRow: any
): boolean => {
  if (fieldIndex < 2) return false; // depthFrom and depthTo are single fields

  const geologySuiteFieldIndex = fieldIndex - 2;
  const dataEntryValue = targetRow.dataEntryValues?.[geologySuiteFieldIndex];
  const fieldType = dataEntryValue?.fieldType;

  // RockType (7) and RockSelect (8) fields are compound when they have number inputs
  return fieldType === FieldType.RockType || fieldType === FieldType.RockSelect;
};

/**
 * Gets the number of sub-fields in a compound field
 */
export const getSubFieldCount = (
  fieldIndex: number,
  targetRow: any
): number => {
  if (isCompoundField(fieldIndex, targetRow)) {
    return 2; // Rock select + number input
  }
  return 1; // Single input field
};

/**
 * Calculates dynamic column width based on field type
 */
export const getDynamicColumnWidth = (field: any): number => {
  if (
    field?.geologyField?.type === FieldType.RockType ||
    field?.geologyField?.type === FieldType.RockSelect
  ) {
    return 250;
  }

  if (field?.geologyField?.type === FieldType.RockTree) {
    return 200;
  }

  if (field?.geologyField?.type === FieldType.Description) {
    return 200;
  }

  return 150; // Default width
};

/**
 * Font size adjustment utility
 */
export const adjustFontSize = (
  currentSize: number,
  increment: boolean
): number => {
  const newSize = increment ? currentSize + 1 : currentSize - 1;
  return Math.max(10, Math.min(20, newSize));
};

/**
 * Filters rows based on search text
 */
export const createFilteredRows = (
  rows: LoggingRowData[],
  searchText: string
): LoggingRowData[] => {
  if (!searchText.trim()) {
    return rows;
  }

  return rows.filter((row) => {
    // Search in depth values
    if (
      row?.depthFrom?.toString().includes(searchText) ||
      row?.depthTo?.toString().includes(searchText)
    ) {
      return true;
    }

    // Search in field values
    return row.dataEntryValues.some((dataEntry) => {
      switch (dataEntry.fieldType) {
        case FieldType.Description:
          return dataEntry.description
            ?.toLowerCase()
            .includes(searchText.toLowerCase());
        case FieldType.NumberField:
          return dataEntry.numberValue?.toString().includes(searchText);
        case FieldType.DateField:
          return dataEntry.dateValue?.includes(searchText);
        default:
          return false;
      }
    });
  });
};

/**
 * Validation result interface
 */
export interface ValidationResult {
  errors: string[];
  fieldErrors: { [key: string]: string };
}

/**
 * Enhanced validation function with field-specific error tracking
 */
export const validateDepthConstraints = (
  rows: LoggingRowData[],
  geologySuiteFields?: any[]
): ValidationResult => {
  const errors: string[] = [];
  const fieldErrors: { [key: string]: string } = {};

  rows.forEach((row, index) => {
    // Ensure depth values are numbers and handle potential string inputs
    const rawDepthFrom = Number(row.depthFrom);
    const rawDepthTo = Number(row.depthTo);

    // Skip validation if depth values are invalid numbers
    if (isNaN(rawDepthFrom) || isNaN(rawDepthTo)) {
      const errorMsg = `Row ${index + 1
        }: Invalid depth values (must be numbers)`;
      errors.push(errorMsg);

      if (isNaN(rawDepthFrom)) {
        fieldErrors[`rows.${index}.depthFrom`] = "Must be a valid number";
      }
      if (isNaN(rawDepthTo)) {
        fieldErrors[`rows.${index}.depthTo`] = "Must be a valid number";
      }
      return;
    }

    // Round to 2 decimal places for consistent comparison
    const depthFrom = roundDepth(rawDepthFrom);
    const depthTo = roundDepth(rawDepthTo);

    // Check depthFrom < depthTo
    if (depthFrom >= depthTo) {
      const errorMsg = `Row ${index + 1}: DF must < depth to`;
      errors.push(errorMsg);

      fieldErrors[`rows.${index}.depthTo`] = `Must > ${depthFrom}`;
    }

    // Check for overlaps with other rows
    const overlappingRows: number[] = [];

    rows.forEach((otherRow, otherIndex) => {
      // Skip the current row
      if (otherIndex === index) {
        return;
      }

      const rawOtherDepthFrom = Number(otherRow.depthFrom);
      const rawOtherDepthTo = Number(otherRow.depthTo);

      // Skip if other row has invalid depth values
      if (isNaN(rawOtherDepthFrom) || isNaN(rawOtherDepthTo)) {
        return;
      }

      // Round to 2 decimal places for consistent comparison
      const otherDepthFrom = roundDepth(rawOtherDepthFrom);
      const otherDepthTo = roundDepth(rawOtherDepthTo);

      // Check for actual overlaps (not just adjacent ranges)
      // Two ranges overlap if: max(start1, start2) < min(end1, end2)
      const overlapStart = Math.max(depthFrom, otherDepthFrom);
      const overlapEnd = Math.min(depthTo, otherDepthTo);

      if (overlapStart < overlapEnd) {
        overlappingRows.push(otherIndex + 1); // +1 for 1-based indexing
      }
    });

    if (overlappingRows.length > 0) {
      const errorMsg = `Row ${index + 1
        }: Depth range (${depthFrom}-${depthTo}) overlaps with row(s) ${overlappingRows.join(
          ", "
        )}`;
      errors.push(errorMsg);

      fieldErrors[
        `rows.${index}.depthFrom`
      ] = `Overlaps with ${overlappingRows.join(", ")}`;
      fieldErrors[
        `rows.${index}.depthTo`
      ] = `Overlaps with ${overlappingRows.join(", ")}`;
    }

    // Validate mandatory geology suite fields
    if (geologySuiteFields) {
      row.dataEntryValues.forEach((dataEntryValue, dataEntryIndex) => {
        if (dataEntryValue.isMandatory) {
          let isEmpty = false;
          let fieldName = "";

          // Check if the field is empty based on field type
          switch (dataEntryValue.fieldType) {
            case FieldType.NumberField:
              isEmpty =
                dataEntryValue.numberValue === null ||
                dataEntryValue.numberValue === undefined;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.numberValue`;
              break;
            case FieldType.Description:
              isEmpty =
                !dataEntryValue.description ||
                dataEntryValue.description.trim() === "";
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.description`;
              break;
            case FieldType.DateField:
              isEmpty = !dataEntryValue.dateValue;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.dateValue`;
              break;
            case FieldType.Colour:
              isEmpty = !dataEntryValue.colourId;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.colourId`;
              break;
            case FieldType.PickList:
              isEmpty = !dataEntryValue.pickListItemId;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.pickListItemId`;
              break;
            case FieldType.RockType:
            case FieldType.RockSelect:
              // Combined validation: both rockTypeId and numberValue are required
              isEmpty =
                !dataEntryValue.rockTypeId || !dataEntryValue.numberValue;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.rockTypeId`;
              break;
            case FieldType.RockGroup:
              isEmpty = !dataEntryValue.rockTypeId;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.rockTypeId`;
              break;
            case FieldType.RockTree:
              isEmpty = !dataEntryValue.rockNodeId;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.rockNodeId`;
              break;
            default:
              isEmpty = !dataEntryValue.value;
              fieldName = `rows.${index}.dataEntryValues.${dataEntryIndex}.value`;
          }

          if (isEmpty) {
            const geologySuiteField = geologySuiteFields?.find(
              (field: any) => field.id === dataEntryValue.geologysuiteFieldId
            );
            const fieldDisplayName = geologySuiteField?.name || "Field";

            // Specific error messages for combined fields
            if (
              dataEntryValue.fieldType === FieldType.RockType ||
              dataEntryValue.fieldType === FieldType.RockSelect
            ) {
              const missingRock = !dataEntryValue.rockTypeId;
              const missingNumber = !dataEntryValue.numberValue;

              if (missingRock && missingNumber) {
                errors.push(
                  `Row ${index + 1
                  }: ${fieldDisplayName} requires both rock selection and number value`
                );
                fieldErrors[fieldName] = "These fields are required";
              } else if (missingRock) {
                errors.push(
                  `Row ${index + 1
                  }: ${fieldDisplayName} requires rock selection`
                );
                fieldErrors[fieldName] = "Rock selection is required";
              } else if (missingNumber) {
                errors.push(
                  `Row ${index + 1}: ${fieldDisplayName} requires number value`
                );
                fieldErrors[
                  `rows.${index}.dataEntryValues.${dataEntryIndex}.numberValue`
                ] = "Number value is required";
              }
            } else {
              errors.push(`Row ${index + 1}: ${fieldDisplayName} is required`);
              fieldErrors[fieldName] = "This field is required";
            }
          }
        }
      });
    }
  });

  return { errors, fieldErrors };
};

// ============================================================================
// LOGGING IMAGE UTILITY FUNCTIONS
// ============================================================================

/**
 * Transforms image type data into tree structure for TreeSelect component
 */
export const transformImageTypeDataToTreeNodes = (data: any[] | undefined) => {
  if (!data) return [];

  return data.map((imageType: any) => ({
    title: imageType.name,
    value: `type-${imageType.id}`,
    key: `type-${imageType.id}`,
    children: (imageType.imageSubtypes || []).map((subtype: any) => ({
      title: subtype.name,
      value: `subtype-${subtype.id}-type-${imageType.id}`,
      key: `subtype-${subtype.id}-type-${imageType.id}`,
    })),
  }));
};

/**
 * Parses tree node value and extracts type and subtype IDs
 */
export const parseTreeNodeValue = (treeNodeValue: string) => {
  let typeId: number | undefined = undefined;
  let subTypeId: number | undefined = undefined;

  if (!treeNodeValue) {
    return { typeId, subTypeId };
  }

  if (treeNodeValue.startsWith("subtype-")) {
    const parts = treeNodeValue.split("-");
    subTypeId = Number(parts[1]);
    typeId = Number(parts[3]);
  } else if (treeNodeValue.startsWith("type-")) {
    const parts = treeNodeValue.split("-");
    typeId = Number(parts[1]);
  }

  return { typeId, subTypeId };
};

/**
 * Calculates relative start X position for image rows
 */
export const calculateRelativeStartX = (
  imageIndex: number,
  imageRows: any[]
): number => {
  if (imageIndex === 0) return 0;

  let relativeStartX = 0;
  for (let j = 0; j < imageIndex; j++) {
    relativeStartX += imageRows[j].coordinate.Width;
  }
  return relativeStartX;
};

/**
 * Creates an image stack object for logging view
 */
export const createImageStack = (
  imageRow: any,
  imageIndex: number,
  startY: number,
  imageGap: number,
  imageRows: any[]
): LoggingViewStack => {
  const relativeStartX = calculateRelativeStartX(imageIndex, imageRows);

  return {
    id: `${imageRow.id}-${EnumLoggingViewStack.Image}`,
    data: {
      src: imageRow.urlCroppedImage,
      width: imageRow.coordinate.Width,
      height: imageRow.coordinate.Height,
      depthFrom: imageRow.depthFrom,
      depthTo: imageRow.depthTo,
      isShowOCR: true,
      isShowText: true,
      id: imageRow.id,
      x: imageRow.coordinate.X,
      y: imageRow.coordinate.Y,
      relativeStartX,
      relativeEndX: relativeStartX + imageRow.coordinate.Width,
      rockLines: imageRow.rockLines,
    },
    startY: startY + imageGap,
    type: EnumLoggingViewStack.Image,
    index: imageIndex,
  };
};

/**
 * Finds logging bars that intersect with a specific image crop
 */
export const findIntersectingLoggingBars = (
  loggingBarData: any[],
  imageCropId: number
) => {
  if (!loggingBarData || loggingBarData.length === 0) return [];

  return loggingBarData.filter((loggingBar: any) => {
    return (
      loggingBar.startImageCropId === imageCropId ||
      loggingBar.endImageCropId === imageCropId ||
      loggingBar.betweenImageCropIds.includes(imageCropId)
    );
  });
};

/**
 * Calculates logging bar position and width based on its relationship to the image crop
 */
export const calculateLoggingBarDimensions = (
  loggingBar: any,
  imageCropId: number,
  imageWidth: number
) => {
  let xPosition = 0;
  let width = imageWidth; // Default to full width

  if (loggingBar.startImageCropId === imageCropId) {
    // This is the start image crop
    xPosition = loggingBar.startX;
    if (loggingBar.endImageCropId === imageCropId) {
      // Single image bar
      width = loggingBar.endX - loggingBar.startX;
    } else {
      // Bar continues to other images
      width = imageWidth - loggingBar.startX;
    }
  } else if (loggingBar.endImageCropId === imageCropId) {
    // This is the end image crop
    xPosition = 0;
    width = loggingBar.endX;
  } else if (loggingBar.betweenImageCropIds.includes(imageCropId)) {
    // Between image crop - bar spans full width
    xPosition = 0;
    width = imageWidth;
  }

  return { xPosition, width };
};

/**
 * Finds rock group color from data entries based on logging bar depth range
 */
export const findRockGroupColor = (
  loggingBar: any,
  rockFieldId: string,
  filteredDataEntry: any[]
): string => {
  if (filteredDataEntry.length === 0) {
    return DEFAULT_ROCK_GROUP_COLOR;
  }

  // Find matching data entry based on depth range
  const matchingEntry = filteredDataEntry.find((entry: any) => {
    return (
      loggingBar.depthFrom >= entry.depthFrom &&
      loggingBar.depthTo <= entry.depthTo
    );
  });
  console.log('matchingEntry: ', matchingEntry);

  if (!matchingEntry) return DEFAULT_ROCK_GROUP_COLOR;


  // Find rock group color from matching entry
  const rockGroupValue = matchingEntry.dataEntryValues?.find(
    (value: any) =>
      value.geologysuiteFieldId === rockFieldId &&
      value.fieldType === FieldType.RockGroup
  );


  if (rockGroupValue?.rockType?.rockStyle?.fillColor) {
    return rockGroupValue.rockType.rockStyle.fillColor;
  }

  return DEFAULT_ROCK_GROUP_COLOR;
};

/**
 * Creates a hyperspectral overlay stack
 */
export const createHyperspectralOverlayStack = (
  extraImageRow: any,
  currentImageRow: any,
  imageIndex: number,
  startY: number,
  imageGap: number
): LoggingViewStack => {
  const aspectRatio =
    currentImageRow.coordinate.Width / extraImageRow.coordinate.Width;

  return {
    id: `${extraImageRow.id}-${EnumLoggingViewStack.Image}-overlay`,
    data: {
      src: extraImageRow.urlCroppedImage,
      width: currentImageRow.coordinate.Width,
      height: extraImageRow.coordinate.Height * aspectRatio,
      depthFrom: extraImageRow.depthFrom,
      depthTo: extraImageRow.depthTo,
      isShowOCR: false,
      isShowText: false,
      id: currentImageRow.id,
    },
    startY: startY + imageGap,
    type: EnumLoggingViewStack.Overlay,
    index: imageIndex,
    uiProps: {
      opacity: 0.6,
    },
  };
};

/**
 * Creates a hyperspectral below stack
 */
export const createHyperspectralBelowStack = (
  extraImageRow: any,
  currentImageRow: any,
  imageIndex: number,
  startY: number
): LoggingViewStack => {
  const aspectRatio =
    currentImageRow.coordinate.Width / extraImageRow.coordinate.Width;

  return {
    id: `${extraImageRow.id}-${EnumLoggingViewStack.Image}-below`,
    data: {
      src: extraImageRow.urlCroppedImage,
      width: currentImageRow.coordinate.Width,
      height: extraImageRow.coordinate.Height * aspectRatio,
      depthFrom: extraImageRow.depthFrom,
      depthTo: extraImageRow.depthTo,
      isShowOCR: false,
      isShowText: false,
      id: currentImageRow.id,
    },
    startY,
    type: EnumLoggingViewStack.Below,
    index: imageIndex,
  };
};
