import React, { memo, useCallback, useEffect, useRef, useState } from "react";
import { Select, InputNumber } from "antd";
import { Control, Controller, useWatch } from "react-hook-form";
import { ErrorTooltip } from "./error-tooltip";

interface RockSelectOption {
  id: number;
  name?: string;
  code?: string;
  description?: string;
}

interface FieldRockSelectProps {
  control: Control<any>;
  name: string;
  disabled?: boolean;
  mandatory?: boolean;
  options?: RockSelectOption[];
  onKeyDown?: (event: React.KeyboardEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
  id?: string;
  // New props for combined functionality
  numberFieldName?: string;
  unit?: string;
  showNumberInput?: boolean;
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void;
  rowIndex?: number;
  fieldPath?: string;
}

export const FieldRockSelect = memo<FieldRockSelectProps>(
  ({
    control,
    name,
    disabled = false,
    mandatory = false,
    options = [],
    onKeyDown,
    onFocus,
    onBlur,
    className,
    id,
    numberFieldName,
    unit,
    showNumberInput = false,
    onFieldChange,
    rowIndex,
    fieldPath,
  }) => {
    const [searchValue, setSearchValue] = useState("");
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        const arrowKeys = [
          "ArrowUp",
          "ArrowDown",
          "ArrowLeft",
          "ArrowRight",
          "Tab",
        ];

        if (arrowKeys.includes(event.key)) {
          if (event.shiftKey) {
            setDropdownOpen(true);
          } else {
            setDropdownOpen(false);
            event.preventDefault();
          }

          onKeyDown?.(event);
        }
      },
      [onKeyDown]
    );

    const handleFocus = useCallback(() => {
      onFocus?.();
    }, [onFocus]);

    const handleBlur = useCallback(() => {
      onBlur?.();
    }, [onBlur]);

    // Separate focus handlers for sub-fields in compound components
    const handleRockSelectFocus = useCallback(() => {
      onFocus?.();
    }, [onFocus]);

    const handleNumberInputFocus = useCallback(() => {
      onFocus?.();
    }, [onFocus]);

    // Create sorted options that prioritize entries beginning with search input
    const createSortedOptions = useCallback(
      (searchValue: string = "") => {
        const inputLower = searchValue.toLowerCase();

        return options
          .filter((option) => {
            const nameLower = option.name?.toLowerCase() || "";
            const codeLower = option.code?.toLowerCase() || "";
            return (
              nameLower.includes(inputLower) || codeLower.includes(inputLower)
            );
          })
          .map((option) => ({
            label: option.name,
            value: option.id,
            originalOption: option,
          }))
          .sort((a, b) => {
            const nameA = a.originalOption.name?.toLowerCase() || "";
            const codeA = a.originalOption.code?.toLowerCase() || "";
            const nameB = b.originalOption.name?.toLowerCase() || "";
            const codeB = b.originalOption.code?.toLowerCase() || "";

            // Check if entries start with the search input
            const aStartsWith =
              nameA.startsWith(inputLower) || codeA.startsWith(inputLower);
            const bStartsWith =
              nameB.startsWith(inputLower) || codeB.startsWith(inputLower);

            // Prioritize entries that start with the search input
            if (aStartsWith && !bStartsWith) return -1;
            if (!aStartsWith && bStartsWith) return 1;

            // If both start with or both don't start with, sort alphabetically
            return nameA.localeCompare(nameB) || codeA.localeCompare(codeB);
          });
      },
      [options]
    );
    const selectOptions = createSortedOptions(searchValue);

    // If showNumberInput is true, render combined rock select + number input
    if (showNumberInput && numberFieldName) {
      // Watch both field values for combined validation
      const rockValue = useWatch({ control, name });
      const numberValue = useWatch({ control, name: numberFieldName });

      // Combined validation function
      const validateCombinedField = (
        value: any,
        fieldType: "rock" | "number"
      ) => {
        if (!mandatory) return true;

        const hasRockValue =
          rockValue !== null && rockValue !== undefined && rockValue !== "";
        const hasNumberValue =
          numberValue !== null &&
          numberValue !== undefined &&
          numberValue !== "";

        // Both fields are required when mandatory
        if (!hasRockValue || !hasNumberValue) {
          return fieldType === "rock"
            ? "These fields are required"
            : "These fields are required";
        }
        return true;
      };

      return (
        <div className="grid grid-cols-12 gap-2 w-full">
          <Controller
            name={name}
            control={control}
            rules={{
              validate: (value) => validateCombinedField(value, "rock"),
            }}
            render={({ field, fieldState: { error } }) => (
              <div className="col-span-8">
                <Select
                  {...field}
                  id={id}
                  disabled={disabled}
                  placeholder="Select rock"
                  className={`w-full ${className || ""}`}
                  options={selectOptions}
                  allowClear
                  showSearch
                  open={dropdownOpen}
                  onDropdownVisibleChange={(open) => {
                    setDropdownOpen(open);
                  }}
                  virtual={selectOptions.length > 100}
                  onSearch={(value) => {
                    setSearchValue(value);
                  }}
                  filterOption={false}
                  onKeyDown={handleKeyDown}
                  onFocus={handleRockSelectFocus}
                  onBlur={handleBlur}
                  onChange={(value) => {
                    field.onChange(value);

                    // Trigger row status update
                    if (
                      onFieldChange &&
                      typeof rowIndex === "number" &&
                      fieldPath
                    ) {
                      onFieldChange(rowIndex, fieldPath, value);
                    }
                  }}
                />
                <ErrorTooltip error={error} />
              </div>
            )}
          />
          <Controller
            name={numberFieldName}
            control={control}
            rules={{
              validate: (value) => validateCombinedField(value, "number"),
            }}
            render={({
              field: numberField,
              fieldState: { error: numberError },
            }) => (
              <div className="col-span-4">
                <div className="flex items-center gap-1">
                  <InputNumber
                    {...numberField}
                    id={id ? `${id}-number` : undefined}
                    disabled={disabled}
                    placeholder="Number"
                    className="flex-1 min-w-[80px]"
                    onKeyDown={handleKeyDown}
                    onFocus={handleNumberInputFocus}
                    onBlur={handleBlur}
                    onChange={(value) => {
                      numberField.onChange(value);

                      // Trigger row status update for number field
                      if (
                        onFieldChange &&
                        typeof rowIndex === "number" &&
                        numberFieldName
                      ) {
                        onFieldChange(rowIndex, numberFieldName, value);
                      }
                    }}
                  />
                  {unit && (
                    <span className="text-sm text-gray-600 whitespace-nowrap">
                      {unit}
                    </span>
                  )}
                </div>
                <ErrorTooltip error={numberError} />
              </div>
            )}
          />
        </div>
      );
    }

    // Default single rock selector
    return (
      <Controller
        name={name}
        control={control}
        rules={{
          required: mandatory ? "This field is required" : false,
        }}
        render={({ field, fieldState: { error } }) => (
          <div className="w-full">
            <Select
              {...field}
              id={id}
              disabled={disabled}
              placeholder="Select rock"
              className={`w-full ${className || ""}`}
              options={selectOptions}
              allowClear
              showSearch
              open={dropdownOpen}
              onDropdownVisibleChange={(open) => {
                setDropdownOpen(open);
              }}
              virtual={selectOptions.length > 100}
              onSearch={(value) => {
                setSearchValue(value);
              }}
              filterOption={false}
              onKeyDown={handleKeyDown}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onChange={(value) => {
                field.onChange(value);

                // Trigger row status update
                if (
                  onFieldChange &&
                  typeof rowIndex === "number" &&
                  fieldPath
                ) {
                  onFieldChange(rowIndex, fieldPath, value);
                }
              }}
            />
            <ErrorTooltip error={error} />
          </div>
        )}
      />
    );
  }
);

FieldRockSelect.displayName = "FieldRockSelect";
