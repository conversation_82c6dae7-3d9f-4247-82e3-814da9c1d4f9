import React, { memo, useCallback } from "react";
import { TreeSelect } from "antd";
import { Control, Controller } from "react-hook-form";
import { FolderOutlined } from "@ant-design/icons";
import { GiStonePile } from "react-icons/gi";
import { ErrorTooltip } from "./error-tooltip";

interface RockTreeNode {
  id: number;
  name: string;
  parentId?: number;
  children?: RockTreeNode[];
  nodeType?: number; // 0=Folder, 1=ConcreteRockType, 2=VirtualRockType
  displayColor?: string;
  iconUrl?: string | null;
}

interface FieldRockTreeProps {
  control: Control<any>;
  name: string;
  disabled?: boolean;
  mandatory?: boolean;
  treeData?: RockTreeNode[];
  onKeyDown?: (event: React.KeyboardEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
  id?: string;
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void;
  rowIndex?: number;
  fieldPath?: string;
}

export const FieldRockTree = memo<FieldRockTreeProps>(
  ({
    control,
    name,
    disabled = false,
    mandatory = false,
    treeData = [],
    onKeyDown,
    onFocus,
    onBlur,
    className,
    id,
    onFieldChange,
    rowIndex,
    fieldPath,
  }) => {
    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        // Handle tab navigation
        if (
          event.key === "Tab" ||
          event.key === "ArrowLeft" ||
          event.key === "ArrowRight" ||
          event.key === "ArrowUp" ||
          event.key === "ArrowDown"
        ) {
          onKeyDown?.(event);
        }
      },
      [onKeyDown]
    );

    const handleFocus = useCallback(() => {
      onFocus?.();
    }, [onFocus]);

    const handleBlur = useCallback(() => {
      onBlur?.();
    }, [onBlur]);

    // Convert tree data to TreeSelect format with icon support
    const convertTreeData = (nodes: RockTreeNode[]): any[] => {
      return nodes.map((node) => {
        // Determine if this is a rock node (nodeType 1 or 2) or folder (nodeType 0 or undefined)
        const isRockNode = node.nodeType === 1 || node.nodeType === 2;
        const isFolderNode = !isRockNode; // Folder nodes are non-rock nodes

        const icon = isRockNode ? (
          <GiStonePile className="mr-2 text-lg min-w-5" />
        ) : (
          <FolderOutlined className="mr-2 text-yellow-500 text-lg min-w-5" />
        );

        return {
          title: (
            <span className="flex items-center">
              {icon}
              <span style={{ color: node.displayColor || "inherit" }}>
                {node.name}
              </span>
            </span>
          ),
          nameForSearch: node.name, // For filtering
          value: node.id,
          key: node.id,
          selectable: !isFolderNode, // Only rock nodes are selectable
          disabled: isFolderNode, // Folder nodes are disabled for selection
          children: node.children ? convertTreeData(node.children) : undefined,
        };
      });
    };

    const treeSelectData = convertTreeData(treeData);

    return (
      <Controller
        name={name}
        control={control}
        rules={{
          required: mandatory ? "This field is required" : false,
        }}
        render={({ field, fieldState: { error } }) => (
          <div className="w-full">
            <TreeSelect
              {...field}
              id={id}
              disabled={disabled}
              placeholder="Select from tree"
              className={`w-full ${className || ""}`}
              treeData={treeSelectData}
              dropdownStyle={{
                maxHeight: 400,
                overflow: "auto",
                minWidth: "min(300px, 90vw)", // Responsive: 300px on desktop, 90% viewport width on mobile
                maxWidth: "min(600px, 95vw)", // Responsive: 600px on desktop, 95% viewport width on mobile
              }}
              allowClear
              showSearch
              virtual={treeSelectData.length > 100}
              treeDefaultExpandAll={false}
              filterTreeNode={(input, node) => {
                // Use nameForSearch property if available, fallback to title
                const searchText =
                  (node as any).nameForSearch || String(node.title || "");
                return searchText.toLowerCase().includes(input.toLowerCase());
              }}
              onKeyDown={handleKeyDown}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onChange={(value) => {
                field.onChange(value);

                // Trigger row status update
                if (
                  onFieldChange &&
                  typeof rowIndex === "number" &&
                  fieldPath
                ) {
                  onFieldChange(rowIndex, fieldPath, value);
                }
              }}
            />
            <ErrorTooltip error={error} />
          </div>
        )}
      />
    );
  }
);

FieldRockTree.displayName = "FieldRockTree";
