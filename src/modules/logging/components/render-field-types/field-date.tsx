import React, { memo, useCallback } from "react";
import { DatePicker } from "antd";
import { Control, Controller } from "react-hook-form";
import dayjs from "dayjs";
import { ErrorTooltip } from "./error-tooltip";

interface FieldDateProps {
  control: Control<any>;
  name: string;
  disabled?: boolean;
  mandatory?: boolean;
  onKeyDown?: (event: React.KeyboardEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
  id?: string;
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void;
  rowIndex?: number;
  fieldPath?: string;
}

export const FieldDate = memo<FieldDateProps>(
  ({
    control,
    name,
    disabled = false,
    mandatory = false,
    onKeyDown,
    onFocus,
    onBlur,
    className,
    id,
    onFieldChange,
    rowIndex,
    fieldPath,
  }) => {
    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        // Handle tab navigation
        if (
          event.key === "Tab" ||
          event.key === "ArrowLeft" ||
          event.key === "ArrowRight" ||
          event.key === "ArrowUp" ||
          event.key === "ArrowDown"
        ) {
          onKeyDown?.(event);
        }
      },
      [onKeyDown]
    );

    const handleFocus = useCallback(() => {
      onFocus?.();
    }, [onFocus]);

    const handleBlur = useCallback(() => {
      onBlur?.();
    }, [onBlur]);

    return (
      <Controller
        name={name}
        control={control}
        rules={{
          required: mandatory ? "This field is required" : false,
        }}
        render={({ field, fieldState: { error } }) => (
          <div className="w-full">
            <DatePicker
              {...field}
              id={id}
              disabled={disabled}
              placeholder="Select date"
              className={`w-full ${className || ""}`}
              format={[
                "DD/MM/YYYY",
                "MM-DD-YYYY",
                "MM/DD/YYYY",
                "YYYY/MM/DD",
                "YYYY-MM-DD",
                "DD-MM-YYYY",
                "DD.MM.YYYY",
              ]}
              value={field.value ? dayjs(field.value) : null}
              onChange={(date) => {
                // const dateValue = date ? date.format("DD/MM/YYYY") : null;
                field.onChange(date);

                // Trigger row status update
                if (
                  onFieldChange &&
                  typeof rowIndex === "number" &&
                  fieldPath
                ) {
                  onFieldChange(rowIndex, fieldPath, date);
                }
              }}
              onKeyDown={handleKeyDown}
              onFocus={handleFocus}
              onBlur={handleBlur}
            />
            <ErrorTooltip error={error} />
          </div>
        )}
      />
    );
  }
);

FieldDate.displayName = "FieldDate";
