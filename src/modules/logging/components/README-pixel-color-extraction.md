# Pixel Color Extraction Feature

This document describes the pixel color extraction functionality added to the `ImageRowLogging` component.

## Overview

The pixel color extraction feature allows users to click on the logging image and retrieve the RGB color values at the exact click coordinates. This functionality is useful for analyzing geological features, identifying rock types by color, or any other color-based analysis.

## Implementation Details

### New Props Added to `ImageRowLogging`

```typescript
interface IAppProps {
  // ... existing props
  enablePixelColorExtraction?: boolean;
  onPixelColorExtracted?: (colorData: {
    x: number;
    y: number;
    rgb: { r: number; g: number; b: number };
    alpha: number;
    hex: string;
  }) => void;
}
```

### Key Features

1. **Conditional Activation**: The feature is only active when `enablePixelColorExtraction` is set to `true`
2. **Accurate Coordinate Mapping**: Handles canvas scaling and transformations to get precise pixel coordinates
3. **Multiple Color Formats**: Returns RGB values, alpha channel, and hexadecimal representation
4. **Error Handling**: Includes comprehensive error handling for edge cases
5. **Visual Feedback**: Shows toast notifications with color information
6. **Callback Support**: Allows parent components to handle extracted color data

### Technical Implementation

The implementation uses the following approach:

1. **Canvas Access**: Accesses the underlying HTML5 canvas through Konva's layer system
2. **Coordinate Transformation**: Converts click coordinates to canvas pixel coordinates accounting for scaling
3. **Pixel Data Extraction**: Uses `getImageData()` to extract RGBA values at specific coordinates
4. **Data Processing**: Converts raw pixel data to user-friendly formats

### Code Structure

```typescript
// Extract pixel color data from image at specific coordinates
const extractPixelColorData = (clickedX: number, clickedY: number) => {
  // Get canvas context from Konva layer
  const canvas = layer.getCanvas()._canvas;
  const context = canvas.getContext('2d');
  
  // Calculate actual pixel coordinates
  const canvasX = Math.floor(clickedX * scaleX);
  const canvasY = Math.floor((clickedY - startY) * scaleY);
  
  // Extract pixel data
  const imageData = context.getImageData(canvasX, canvasY, 1, 1);
  const [r, g, b, a] = imageData.data;
  
  // Return formatted color data
  return { x: clickedX, y: clickedY, rgb: { r, g, b }, alpha: a, hex: ... };
};
```

## Usage Examples

### Basic Usage

```typescript
<ImageRowLogging
  {...existingProps}
  enablePixelColorExtraction={true}
  onPixelColorExtracted={(colorData) => {
    console.log('Color at coordinates:', colorData);
  }}
/>
```

### Advanced Usage with State Management

```typescript
const [extractedColors, setExtractedColors] = useState([]);

const handleColorExtraction = (colorData) => {
  // Store color data
  setExtractedColors(prev => [...prev, colorData]);
  
  // Send to API
  api.saveColorData(colorData);
  
  // Update analysis
  updateColorAnalysis(colorData);
};

<ImageRowLogging
  {...existingProps}
  enablePixelColorExtraction={true}
  onPixelColorExtracted={handleColorExtraction}
/>
```

## Color Data Format

The extracted color data has the following structure:

```typescript
{
  x: number;          // Click X coordinate relative to image
  y: number;          // Click Y coordinate relative to image
  rgb: {              // RGB color values (0-255)
    r: number;
    g: number;
    b: number;
  };
  alpha: number;      // Alpha channel (0-255)
  hex: string;        // Hexadecimal color (#RRGGBB)
}
```

## Error Handling

The implementation includes error handling for:

- Missing image reference
- Unavailable canvas context
- Coordinates outside canvas bounds
- Canvas access errors
- Cross-origin image issues

## Performance Considerations

- **Efficient Extraction**: Only extracts 1x1 pixel data for optimal performance
- **Conditional Execution**: Feature only runs when explicitly enabled
- **Error Recovery**: Graceful handling of extraction failures
- **Memory Management**: No persistent canvas data storage

## Browser Compatibility

The feature uses standard HTML5 Canvas APIs and should work in all modern browsers that support:
- `getImageData()` method
- Konva.js canvas library
- React hooks

## Security Considerations

- **CORS Compliance**: Images must be served from same origin or with proper CORS headers
- **Canvas Tainting**: Cross-origin images may cause security restrictions
- **Data Validation**: All extracted data is validated before use

## Future Enhancements

Potential improvements could include:

1. **Area Selection**: Extract colors from rectangular regions
2. **Color Averaging**: Average colors over small areas for noise reduction
3. **Color History**: Built-in color extraction history
4. **Export Functionality**: Export color data to various formats
5. **Color Matching**: Compare extracted colors against predefined palettes

## Troubleshooting

### Common Issues

1. **"Canvas has been tainted" error**: Ensure images are served with proper CORS headers
2. **Coordinates outside bounds**: Check image scaling and positioning
3. **No color data extracted**: Verify canvas context availability
4. **Incorrect coordinates**: Check for proper coordinate transformation

### Debug Tips

- Enable console logging to see extracted color data
- Check browser developer tools for canvas-related errors
- Verify image loading and Konva stage setup
- Test with different image formats and sizes
