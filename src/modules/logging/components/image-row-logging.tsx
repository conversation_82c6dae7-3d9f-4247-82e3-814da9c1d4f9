import { RequestState } from "@/common/configs/app.contants";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { KonvaEventObject, NodeConfig } from "konva/lib/Node";
import { Fragment, useMemo, useRef, useState } from "react";
import { IconPickerItem } from "react-icons-picker";
import { Circle, Group, Image, Line, Rect, Text } from "react-konva";
import { Html } from "react-konva-utils";
import { toast } from "react-toastify";
import useImage from "use-image";
import recoveryRequest from "../api/recovery.api";
import { LoggingLine } from "../interface/logging.interface";
import { ClickDepthData } from "../model/dtos/logging.config";
import {
  EnumCalculateClickedDepth,
  EnumLoggingViewStack,
} from "../model/enum/logging.enum";
import {
  addLoggingLine,
  updateCalculateRecoveryBySelectPoint,
  updateIsDeleteLine,
  updateLoggingContextMenu,
  updateLoggingLine,
  updateMeasurePointsInterval,
  updateOpenModalGeotechData,
  updatePreviewImageUrl,
  updateReviewDepthTo,
  updateSelectedLine,
  updateToggleUpdateOCR,
} from "../redux/loggingSlice";
import { Recovery, RockLineType } from "../types/logging.types";
import { transformSegmentations } from "../utils/logging.utils";
import DraggableIntersectionPoint from "./draggable-intersection-point";
import SegmentationPoint from "./segmentation-point";
export interface IAppProps {
  data: any;
  startY: number;
  directOCRdata: any;
  onChangeText: any;
  onDblClickText: any;
  onChange: any;
  isShowOCR: boolean;
  uiProps?: NodeConfig;
  imageIndex: number;
  type: EnumLoggingViewStack;
  clickedDepthData: ClickDepthData[];
  setModalState: (state: any) => void;
  modalState: any;
  setStateAddOcr: (state: {
    isOpen: boolean;
    x: number;
    y: number;
    rowIndex: number;
  }) => void;
  stateAddOcr: { isOpen: boolean; x: number; y: number; rowIndex: number };
  addOCR: boolean;
  isMeasuring?: boolean;
  onMeasurePoint?: (point: {
    x: number;
    depth: number;
    rowIndex: number;
  }) => void;
  startPointLine: { x: number; rowIndex: number } | null;
  recoveries?: Recovery[];
  refreshImageData?: () => void;
  image: any;
  enablePixelColorExtraction?: boolean;
  onPixelColorExtracted?: (colorData: {
    x: number;
    y: number;
    rgb: { r: number; g: number; b: number };
    alpha: number;
    hex: string;
  }) => void;
}

export function ImageRowLogging({
  data,
  addOCR,
  startY,
  directOCRdata,
  onChange,
  isShowOCR,
  uiProps,
  imageIndex,
  type,
  clickedDepthData,
  setModalState,
  setStateAddOcr,
  recoveries = [],
  isMeasuring = false,
  onMeasurePoint,
  startPointLine,
  image: imageProps,
  refreshImageData,
  enablePixelColorExtraction = true,
  onPixelColorExtracted,
}: IAppProps) {
  const src = data?.src ?? "";
  const [image] = useImage(src, "anonymous"); // Enable CORS for pixel data access
  const imageRef = useRef<any>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [loadingPosition, setLoadingPosition] = useState({ x: 0, y: 0 });

  // Add state for tracking first point in Add mode
  const [firstPoint, setFirstPoint] = useState<{
    x: number;
    y: number;
    rowIndex: number;
    imageCropId: number;
  } | null>(null);
  const [dragPositions, setDragPositions] = useState<{ [key: string]: number }>(
    {}
  );
  // Add state for line hover effect
  const [hoveredLineId, setHoveredLineId] = useState<string | null>(null);
  // Add state for loading while splitting
  const [isSplittingLine, setIsSplittingLine] = useState(false);
  const [splittingLineId, setSplittingLineId] = useState<string | null>(null);
  // Add state for line creation loading
  const [isCreatingLine, setIsCreatingLine] = useState(false);
  const [mousePosition, setMousePosition] = useState<{
    x: number;
    y: number;
  } | null>(null);

  // State for pixel color data
  const [pixelColorData, setPixelColorData] = useState<{
    x: number;
    y: number;
    rgb: { r: number; g: number; b: number };
    alpha: number;
    hex: string;
  } | null>(null);

  // Expose pixel color data through a getter function
  const getLastPixelColorData = () => pixelColorData;

  const dispatch = useAppDispatch();

  // Predefined array of visually distinct colors
  const distinctColors = [
    "#FF5733", // Red-orange
    "#33A8FF", // Blue
    "#33FF57", // Green
    "#F033FF", // Purple
    "#FFFF33", // Yellow
    "#33FFF6", // Cyan
    "#FF33A8", // Pink
    "#A833FF", // Violet
    "#FF9933", // Orange
    "#33FFAA", // Mint
    "#3369FF", // Royal blue
    "#BEFF33", // Lime
    "#FF33F6", // Magenta
    "#33FFD4", // Turquoise
    "#6833FF", // Indigo
    "#FF4D4D", // Bright red
    "#4DFF4D", // Bright green
    "#4D4DFF", // Bright blue
    "#FFD700", // Gold
    "#00CED1", // Dark turquoise
    "#8B4513", // Saddle brown
    "#FF69B4", // Hot pink
    "#1E90FF", // Dodger blue
    "#32CD32", // Lime green
    "#FF8C00", // Dark orange
  ];

  // Function to generate a distinct color based on line ID
  const getLineColor = (lineId: number) => {
    // Use modulo to wrap around the colors array, ensuring consistent color for same ID
    return distinctColors[lineId % distinctColors.length];
  };

  // Function to extract pixel color data from image at specific coordinates
  const extractPixelColorData = (clickedX: number, clickedY: number) => {
    if (!imageRef.current || !image) {
      console.warn(
        "Image reference or image not available for pixel extraction"
      );
      return null;
    }

    try {
      // Get the stage and layer to access the canvas
      const stage = imageRef.current.getStage();
      if (!stage) {
        console.warn("Stage not available for pixel extraction");
        return null;
      }

      const layer = imageRef.current.getLayer();
      if (!layer) {
        console.warn("Layer not available for pixel extraction");
        return null;
      }

      // Get the canvas context from the layer
      const canvas = layer.getCanvas()._canvas;
      const context = canvas.getContext("2d");

      if (!context) {
        console.warn("Canvas context not available for pixel extraction");
        return null;
      }

      // Calculate the actual pixel coordinates on the canvas
      // Account for the image position and scaling
      const scaleX = stage.scaleX() || 1;
      const scaleY = stage.scaleY() || 1;

      // Convert clicked coordinates to canvas coordinates
      const canvasX = Math.floor(clickedX * scaleX);
      const canvasY = Math.floor((clickedY - startY) * scaleY);

      // Ensure coordinates are within canvas bounds
      if (
        canvasX < 0 ||
        canvasY < 0 ||
        canvasX >= canvas.width ||
        canvasY >= canvas.height
      ) {
        console.warn("Click coordinates are outside canvas bounds");
        return null;
      }

      // Extract pixel data (1x1 pixel)
      const imageData = context.getImageData(canvasX, canvasY, 1, 1);
      const pixelData = imageData.data;

      // Extract RGB values
      const r = pixelData[0];
      const g = pixelData[1];
      const b = pixelData[2];
      const a = pixelData[3];

      // Convert to hex
      const hex = `#${r.toString(16).padStart(2, "0")}${g
        .toString(16)
        .padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;

      const colorData = {
        x: clickedX,
        y: clickedY,
        rgb: { r, g, b },
        alpha: a,
        hex: hex,
      };

      // Log the color data to console
      console.log("Pixel Color Data:", colorData);

      // Update state with the color data
      setPixelColorData(colorData);

      // Call the callback function if provided
      if (onPixelColorExtracted) {
        onPixelColorExtracted(colorData);
      }

      // Show toast notification with color information
      toast.info(
        `Color at (${Math.round(clickedX)}, ${Math.round(
          clickedY - startY
        )}): RGB(${r}, ${g}, ${b}) - ${hex}`,
        {
          position: "top-right",
          autoClose: 3000,
        }
      );

      return colorData;
    } catch (error) {
      console.error("Error extracting pixel color data:", error);

      // Provide specific error messages for common issues
      if (error instanceof Error) {
        if (
          error.message.includes("tainted") ||
          error.message.includes("cross-origin")
        ) {
          toast.error(
            "Cannot extract pixel data: Image is from a different domain. Please ensure CORS headers are configured.",
            {
              position: "top-right",
              autoClose: 5000,
            }
          );
          console.warn(
            "CORS Solution: Ensure the image server includes 'Access-Control-Allow-Origin' header or serve images from the same domain."
          );
        } else if (error.message.includes("getImageData")) {
          toast.error(
            "Canvas access denied. Check image loading and CORS configuration.",
            {
              position: "top-right",
              autoClose: 4000,
            }
          );
        } else {
          toast.error("Failed to extract pixel color data: " + error.message, {
            position: "top-right",
            autoClose: 3000,
          });
        }
      } else {
        toast.error("Failed to extract pixel color data");
      }

      return null;
    }
  };

  const handleIntersectionDragEnd = (
    index: number,
    newX: number,
    lineId: number,
    isEndPoint: boolean = false
  ) => {
    // Update the visual position only when drag is completed
    setDragPositions((prev) => ({
      ...prev,
      [`line-${lineId}-${isEndPoint ? "end" : "start"}`]: newX,
    }));

    // Call API to save the changes
    // Find the updated segment to get coordinates
    const rockLine = segmentationLines?.find((line) => line.id === lineId);

    if (rockLine) {
      // Create parameters for the API call
      const apiParams = {
        id: lineId,
        startX: isEndPoint ? rockLine.startX : newX,
        endX: isEndPoint ? newX : rockLine.endX,
      };

      // Call the API to update the rock line
      recoveryRequest
        .updateRockLine(apiParams)
        .then((response) => {
          if (response.state === RequestState.success) {
            toast.success("Rock line updated successfully", {
              position: "top-center",
              autoClose: 900,
            });
            // The visual position is already updated locally via setDragPositions
            if (refreshImageData) {
              refreshImageData();
            }
          } else if (response.state === "error") {
            toast.error(response.message || "Failed to update rock line");
            // Revert the visual position on error
            setDragPositions((prev) => {
              const newPositions = { ...prev };
              delete newPositions[
                `line-${lineId}-${isEndPoint ? "end" : "start"}`
              ];
              return newPositions;
            });
          }
        })
        .catch((error) => {
          toast.error("Error updating rock line");
          // Revert the visual position on error
          setDragPositions((prev) => {
            const newPositions = { ...prev };
            delete newPositions[
              `line-${lineId}-${isEndPoint ? "end" : "start"}`
            ];
            return newPositions;
          });
        });
    }
  };

  const depthFrom = Number(data?.depthFrom?.toFixed(2));
  const depthTo = Number(data?.depthTo?.toFixed(2));

  const contextMenu = useAppSelector((state) => state?.logging?.contextMenu);
  const lines = useAppSelector((state) => state?.logging?.lines);
  const segmentationResults = useAppSelector(
    (state) => state.logging.segmentations
  );
  const segmentationResultsDetail = useAppSelector(
    (state) => state.logging.segmentationsDetail
  );
  const isMergeLine = useAppSelector((state) => state.logging.isMergeLine);
  const selectedLine = useAppSelector((state) => state.logging.selectedLine);
  const isAddLine = useAppSelector((state) => state.logging.isAddLine);
  const isDeleteLine = useAppSelector((state) => state.logging.isDeleteLine);
  const calculateClickedMode = useAppSelector(
    (state) => state.logging.calculateClickedDepth
  );
  const isShowSegmentation = useAppSelector(
    (state) => state.logging.isShowSegmentation
  );
  const isShowPercentageRecovery = useAppSelector(
    (state) => state.logging.isShowPercentageRecovery
  );
  const selectedDrillhole = useAppSelector(
    (state) => state.logging.selectedDrillHole
  );

  const selectedRockLineType = useAppSelector(
    (state) => state.logging.selectedRockLineType
  );

  // Calculate ocr text sizing based on image width
  const ocrSizing = useMemo(() => {
    // Reference width adjusted to get the right base size
    const referenceWidth = 1500;

    // Calculate base scale factor
    let scaleFactor = data.width / referenceWidth;

    // Apply min/max thresholds to the scale factor
    scaleFactor = Math.max(0.3, Math.min(1.5, scaleFactor));

    // Calculate dimensions based on scale factor - increased by 1.5x from previous values
    const fontSize = Math.round(97 * scaleFactor);
    const rectHeight = Math.round(120 * scaleFactor);
    const textHeight = Math.round(105 * scaleFactor);
    const rectYOffset = Math.round(-38 * scaleFactor);
    const textYOffset = Math.round(-15 * scaleFactor);
    const strokeWidth = Math.max(1, Math.round(7 * scaleFactor));
    const charWidthMultiplier = Math.round(75 * scaleFactor);
    const textXOffset = Math.round(8 * scaleFactor);

    return {
      fontSize,
      rectHeight,
      textHeight,
      rectYOffset,
      textYOffset,
      strokeWidth,
      charWidthMultiplier,
      textXOffset,
    };
  }, [data.width]);

  const calculateClickDepthOCR = (
    relativeX: number,
    clickedDepthData: ClickDepthData[]
  ) => {
    let clickedDepth: number = -Infinity;
    let considerStart;
    let considerEnd;
    const sortedPoints = clickedDepthData.sort(
      (a, b) => a.relativeX - b.relativeX
    );
    // Find the points that surround relativeStartX
    for (let i = 0; i < sortedPoints.length - 1; i++) {
      const currentPoint = sortedPoints[i];
      const nextPoint = sortedPoints[i + 1];

      if (
        currentPoint.relativeX <= relativeX &&
        nextPoint.relativeX >= relativeX
      ) {
        considerStart = currentPoint;
        considerEnd = nextPoint;
      }
    }
    if (!considerStart || !considerEnd) return 0;

    clickedDepth =
      Number(considerStart.depth) +
      ((relativeX - considerStart.relativeX) /
        (considerEnd.relativeX - considerStart.relativeX)) *
        (considerEnd.depth - considerStart.depth);

    return parseFloat(clickedDepth?.toFixed(2));
  };

  const calculateClickDepthStandard = (clickedX: number) => {
    return parseFloat(
      (depthFrom + (clickedX / data?.width) * (depthTo - depthFrom)).toFixed(2)
    );
  };

  const structure = useAppSelector((state) => state.structure.detail);
  const isInterval = structure?.selector === 1;
  const isPoint = structure?.selector === 2;
  const measurePointsInterval = useAppSelector(
    (state) => state.logging.measurePointsInterval
  );

  // Function to handle clicks on the image
  const handleImageClick = async (e: KonvaEventObject<MouseEvent>) => {
    const stage = e.target.getStage();
    const pointerPos = stage?.getPointerPosition();
    if (!pointerPos || !stage?.scale()?.x || !data?.width) return;
    const clickedX = (pointerPos.x - stage?.x()) / (stage?.scale()?.x ?? 1);
    const clickedY = (pointerPos.y - stage?.y()) / (stage?.scale()?.y ?? 1);

    // Extract pixel color data on click if enabled
    if (enablePixelColorExtraction) {
      extractPixelColorData(clickedX, clickedY);
    }

    // Handle Add Line mode
    if (isAddLine) {
      const addLineClickedY = startY + data.height / 2; // Middle of the image vertically

      if (!firstPoint) {
        // First point selection
        setFirstPoint({
          x: clickedX,
          y: addLineClickedY,
          rowIndex: imageIndex,
          imageCropId: data.id,
        });
        toast.info("First point selected. Click to place the second point.");
        return;
      } else {
        // Second point selection - verify same image
        if (
          firstPoint.rowIndex !== imageIndex ||
          firstPoint.imageCropId !== data.id
        ) {
          toast.error("Both points must be on the same image");
          return;
        }

        // Check if points are too close
        const MIN_DISTANCE = 5; // Minimum 5px distance
        if (Math.abs(firstPoint.x - clickedX) < MIN_DISTANCE) {
          toast.warning(
            "Points are too close together. Please select a point further away."
          );
          return;
        }

        // Prevent creating multiple lines at once
        if (isCreatingLine) {
          toast.info("Line creation in progress, please wait");
          return;
        }

        // Create the rock line
        setIsCreatingLine(true);
        try {
          const createParams = {
            type: selectedRockLineType,
            startX: firstPoint.x,
            startY: firstPoint.y - startY, // Adjust for startY offset
            endX: clickedX,
            endY: addLineClickedY - startY, // Adjust for startY offset
            depthFrom: calculateClickDepthStandard(firstPoint.x),
            depthTo: calculateClickDepthStandard(clickedX),
            imageCropId: data.id,
            rowIndex: imageIndex, // Add rowIndex field
          };

          const result = await recoveryRequest.createRockLine(createParams);

          if (result.state === RequestState.error) {
            toast.error(result.message || "Failed to create rock line");
          } else {
            toast.success("Rock line created successfully");
            // Refresh UI
            if (refreshImageData) {
              refreshImageData();
            }
          }
        } catch (error) {
          toast.error("Error creating rock line");
        } finally {
          setIsCreatingLine(false);
          // Reset first point but keep Add mode active
          setFirstPoint(null);
        }
        return;
      }
    }

    if (isMergeLine || isSplitLine || isDeleteLine || isAddLine) {
      return;
    }

    // Handle Add OCR mode - this should be checked before other conditions
    if (addOCR) {
      setStateAddOcr({
        isOpen: true,
        x: clickedX + data.x,
        y: data.y + pointerPos.y,
        rowIndex: imageIndex,
      });
      return;
    }

    if (isPoint && loggingSuiteMode === "Geotech") {
      dispatch(updateOpenModalGeotechData(true));
    }

    if (contextMenu.isRightClick) return;

    if (isInterval) {
      const response = await recoveryRequest.calculateDepthBySelectPoint({
        drillHoleId: Number(selectedDrillhole!.value),
        imageCropId: data.id,
        x: clickedX,
      });
      if (!measurePointsInterval?.start && !measurePointsInterval?.end) {
        dispatch(
          updateMeasurePointsInterval({
            start: {
              x: clickedX,
              depth: response.data ?? 0,
              imageCropId: data.id,
            },
          })
        );
      }
      if (measurePointsInterval?.start && !measurePointsInterval?.end) {
        if (
          response.data &&
          response.data > measurePointsInterval?.start?.depth
        ) {
          dispatch(
            updateMeasurePointsInterval({
              ...measurePointsInterval,
              end: {
                x: clickedX,
                depth: response.data ?? 0,
                imageCropId: data.id,
              },
            })
          );
          dispatch(updateOpenModalGeotechData(true));
        } else {
          dispatch(
            updateMeasurePointsInterval({
              start: {
                x: clickedX,
                depth: response.data ?? 0,
                imageCropId: data.id,
              },
              end: measurePointsInterval?.start,
            })
          );
          dispatch(updateOpenModalGeotechData(true));
        }
      }
    }

    // Handle Measuring mode
    if (isMeasuring) {
      setIsCalculating(true);
      setLoadingPosition({ x: clickedX, y: startY + data.height / 2 });

      try {
        const response = await recoveryRequest.calculateDepthBySelectPoint({
          drillHoleId: Number(selectedDrillhole!.value),
          imageCropId: data.id,
          x: clickedX,
        });

        if (response.state === "error") {
          toast.error(response.message || "Failed to calculate depth");
          return;
        }

        const clickedDepth = Number(response.data);
        onMeasurePoint?.({
          x: clickedX,
          depth: clickedDepth,
          rowIndex: imageIndex,
        });
      } catch (error) {
        toast.error("Error calculating depth");
      } finally {
        setIsCalculating(false);
      }
      return;
    }
    // Regular click handling
    let clickedDepth: number = -Infinity;

    if (calculateClickedMode === EnumCalculateClickedDepth.Standard) {
      clickedDepth = calculateClickDepthStandard(clickedX);
    } else if (
      calculateClickedMode === EnumCalculateClickedDepth.OcrCalculation
    ) {
      const relativeClickedX = clickedX + data.relativeStartX;
      clickedDepth = calculateClickDepthOCR(relativeClickedX, clickedDepthData);
      if (clickedDepth === 0 || !clickedDepth) {
        clickedDepth = calculateClickDepthStandard(clickedX);
      }
    } else if (
      calculateClickedMode === EnumCalculateClickedDepth.ApiCalculation
    ) {
      setIsCalculating(true);
      setLoadingPosition({ x: clickedX, y: startY + data.height / 2 });
      try {
        const response = await recoveryRequest.calculateDepthBySelectPoint({
          drillHoleId: Number(selectedDrillhole!.value),
          imageCropId: data.id,
          x: clickedX,
        });
        dispatch(
          updateCalculateRecoveryBySelectPoint({
            imageCropId: data.id,
            x: clickedX,
            depth: response.data ?? 0,
          })
        );

        if (response.state === "error") {
          toast.error(response.message || "Failed to calculate depth from API");
          return;
        } else {
          clickedDepth = Number(response.data);
        }
      } catch (error) {
        toast.error("Error calculating depth from API");
      } finally {
        setIsCalculating(false);
      }
    }

    dispatch(updateReviewDepthTo(Number(clickedDepth)));
    const findNotSaveLine = lines.find((line) => !line.isSave);

    // Create a vertical line (from top to bottom of the canvas)
    if (!findNotSaveLine) {
      const newLine: LoggingLine = {
        id: lines.length,
        isSave: false,
        data: {
          x: clickedX,
          id: data.id,
        },
      };
      dispatch(addLoggingLine(newLine));
      return;
    }
    dispatch(
      updateLoggingLine({
        ...findNotSaveLine,
        data: {
          x: clickedX,
          id: data.id,
        },
      })
    );
  };

  // Handle mouse movement for the temporary line visualization
  const handleMouseMove = (e: KonvaEventObject<MouseEvent>) => {
    if (isAddLine && firstPoint && firstPoint.rowIndex === imageIndex) {
      const stage = e.target.getStage();
      const pointerPos = stage?.getPointerPosition();
      if (!pointerPos || !stage?.scale()?.x) return;

      const x = (pointerPos.x - stage?.x()) / (stage?.scale()?.x ?? 1);
      const y = startY + data.height / 2;
      setMousePosition({ x, y });
    } else if (mousePosition) {
      setMousePosition(null);
    }
  };

  const handleContextMenu = (e: KonvaEventObject<MouseEvent>) => {
    e.evt.preventDefault();
    const stage = e.target.getStage();
    const pointerPos = stage?.getPointerPosition();
    if (!pointerPos || !stage?.scale()?.x) return;

    dispatch(
      updateLoggingContextMenu({
        visible: true,
        isRightClick: true,
        x: (pointerPos.x - stage?.x()) / (stage?.scale()?.x ?? 1),
        y: startY,
      })
    );

    dispatch(
      updatePreviewImageUrl({
        url: src,
      })
    );
  };

  const handleCloseMenu = () => {
    dispatch(
      updateLoggingContextMenu({
        visible: false,
        isRightClick: false,
      })
    );
  };

  const handlePreviewImage = (event) => {
    event.evt.preventDefault();
    handleCloseMenu();
    dispatch(
      updatePreviewImageUrl({
        visible: true,
      })
    );
  };

  const segmentations = useMemo(() => {
    if (selectedRockLineType === RockLineType.RQD) {
      return segmentationResultsDetail.filter(
        (segmentation) =>
          segmentation.rowIndex === imageIndex + 1 &&
          segmentation.Class === "core"
      );
    }
    return segmentationResults.filter(
      (segmentation) =>
        segmentation.rowIndex === imageIndex + 1 &&
        segmentation.Class === "core"
    );
  }, [segmentationResults, segmentationResultsDetail, selectedRockLineType]);

  const transformedSegmentations = transformSegmentations(
    segmentations,
    type,
    {
      x: data.x,
      y: data.y,
      height: data.height,
    },
    startY
  );

  function updateRockLineCoordinate(rockLine: any): {
    startX: number;
    startY: number;
    endX: number;
    endY: number;
    id: number;
    imageCropId: number;
  } {
    return {
      startX: rockLine.startX,
      startY: rockLine.startY + startY,
      endX: rockLine.endX,
      endY: rockLine.endY + startY,
      id: rockLine.id,
      imageCropId: rockLine.imageCropId,
    };
  }

  const segmentationLines =
    (data?.rockLines ?? [])
      .filter((rockLine) => rockLine.type === selectedRockLineType)
      ?.map(updateRockLineCoordinate) ?? [];

  // Touch handling refs
  const touchStartTimeRef = useRef<number>(0);
  const touchStartPosRef = useRef<{ x: number; y: number } | null>(null);
  const isDraggingRef = useRef(false);
  const hbox = useRef<any>(null);
  const textOCRs = directOCRdata.filter((data) => data.rowIndex === imageIndex);

  // Touch event handlers
  const handleTouchStart = (e: any) => {
    e.evt.preventDefault();
    const touches = e.evt.touches;

    if (touches.length === 1) {
      // Record start time and position for single touch
      touchStartTimeRef.current = Date.now();
      touchStartPosRef.current = {
        x: touches[0].clientX,
        y: touches[0].clientY,
      };
      isDraggingRef.current = false;
    }
  };

  const handleTouchMove = (e: any) => {
    const touches = e.evt.touches;
    if (touches.length === 1 && touchStartPosRef.current) {
      // Calculate movement distance
      const dx = touches[0].clientX - touchStartPosRef.current.x;
      const dy = touches[0].clientY - touchStartPosRef.current.y;
      const moveDistance = Math.sqrt(dx * dx + dy * dy);

      // If moved more than 10px, consider it a drag
      if (moveDistance > 10) {
        isDraggingRef.current = true;
      }
    }
  };

  const handleTouchEnd = (e: any) => {
    const touchDuration = Date.now() - touchStartTimeRef.current;

    // Only trigger click if:
    // 1. It was a short touch (less than 200ms)
    // 2. Not identified as dragging
    // 3. Was a single finger touch
    if (
      touchDuration < 200 &&
      !isDraggingRef.current &&
      e.evt.touches.length === 0
    ) {
      handleImageClick(e);
    }

    // Reset refs
    touchStartTimeRef.current = 0;
    touchStartPosRef.current = null;
    isDraggingRef.current = false;
  };
  const loggingSuiteMode = useAppSelector(
    (state) => state.logging.loggingSuiteMode
  );
  const geotechDataPoint = useAppSelector(
    (state) => state?.logging?.geotechData
  )?.map((data) => {
    return {
      x: data.x,
      id: data.imageCropId,
    };
  });

  const geotechDataInterval = useAppSelector(
    (state) => state?.logging?.geotechData
  )?.map((data) => {
    return {
      x: data.x,
      xTo: data.xTo,
      id: data.imageCropId,
      idTo: data.imageCropIdTo,
    };
  });
  const isLoadingGeotechData = useAppSelector(
    (state) => state.logging.loadingGeotechData
  );
  const coordinate = imageProps?.croppedImages
    ?.filter((image) => image?.type?.toLowerCase() === "row")
    ?.map((image) => {
      return (JSON.parse(image?.coordinate) as { X: number }).X ?? 0;
    });

  const isSplitLine = useAppSelector((state) => state.logging.isSplitLine);

  // Enhanced handleLineClick to implement line splitting logic
  const handleLineClick = async (
    e: KonvaEventObject<MouseEvent>,
    segmentationName: string,
    lineId: number,
    line: any
  ) => {
    e.cancelBubble = true; // Ngăn sự kiện click lan lên các component cha

    // If neither split nor merge mode is active, return
    if (!isSplitLine && !isMergeLine && !isDeleteLine) return;

    const stage = e.target.getStage();
    const pointerPos = stage?.getPointerPosition();
    if (!pointerPos || !stage?.scale()?.x) return;

    // Tính toán vị trí thực tế của điểm click
    const clickedX = (pointerPos.x - stage?.x()) / (stage?.scale()?.x ?? 1);
    const clickedY = (pointerPos.y - stage?.y()) / (stage?.scale()?.y ?? 1);

    // Handle merge line mode
    if (isMergeLine) {
      if (!selectedLine) {
        // First line selection
        dispatch(updateSelectedLine({ id: lineId, line, segmentationName }));
        toast.info("First line selected. Click another line to merge.");
      } else {
        // Second line selection - perform merge
        if (selectedLine.id === lineId) {
          toast.info(
            "You selected the same line. Please select a different line to merge."
          );
          return;
        }

        // Check if both lines belong to the same imageCropId
        // First check if imageCropId exists on both lines
        if (
          !line.hasOwnProperty("imageCropId") ||
          !selectedLine.line.hasOwnProperty("imageCropId")
        ) {
          toast.error("Cannot merge lines: missing imageCropId property");
          console.log("Cannot merge lines: missing imageCropId property", {
            selectedLine: selectedLine.line,
            currentLine: line,
          });
          dispatch(updateSelectedLine(null));
          return;
        }

        const currentImageCropId = Number(line.imageCropId);
        const selectedImageCropId = Number(selectedLine.line.imageCropId);

        // Ensure both imageCropIds are valid numbers and compare them
        if (
          isNaN(currentImageCropId) ||
          isNaN(selectedImageCropId) ||
          currentImageCropId !== selectedImageCropId
        ) {
          toast.error("Cannot merge lines from different images");
          console.log("Cannot merge lines from different images:", {
            selectedLine: selectedLine.line.imageCropId,
            currentLine: line.imageCropId,
            selectedLineType: typeof selectedLine.line.imageCropId,
            currentLineType: typeof line.imageCropId,
          });
          dispatch(updateSelectedLine(null));
          return;
        }

        // Determine left and right lines
        const isSelectedLineLeft =
          Math.min(selectedLine.line.startX, selectedLine.line.endX) <
          Math.min(line.startX, line.endX);

        const leftLine = isSelectedLineLeft ? selectedLine.line : line;
        const rightLine = isSelectedLineLeft ? line : selectedLine.line;
        const leftLineId = isSelectedLineLeft ? selectedLine.id : lineId;
        const rightLineId = isSelectedLineLeft ? lineId : selectedLine.id;

        // Perform merge operation
        mergeLinesAPI(leftLineId, rightLineId, leftLine, rightLine);
      }
      return;
    }

    // Handle delete line mode
    if (isDeleteLine) {
      try {
        // Request to delete the rock line
        const response = await recoveryRequest.deleteRockLine(lineId);

        if (response.state === RequestState.success) {
          toast.success("Rock line deleted successfully");

          // Reset the selected line
          dispatch(updateSelectedLine(null));

          // Exit delete mode after successful deletion
          dispatch(updateIsDeleteLine(false));

          // Update the UI to reflect the deletion
          if (refreshImageData) {
            refreshImageData();
          }
        } else {
          toast.error(response.message || "Failed to delete rock line");
        }
      } catch (error) {
        toast.error("An error occurred while deleting the rock line");
      }
      return;
    }

    // Make sure clickedX is within the line segment
    const startX =
      typeof line === "object" && "startX" in line ? line.startX : 0;
    const endX = typeof line === "object" && "endX" in line ? line.endX : 0;

    if (
      clickedX < Math.min(startX, endX) ||
      clickedX > Math.max(startX, endX)
    ) {
      return; // Click is outside the line segment
    }

    // Handle split line mode - rest of the function for split line mode
    if (isSplitLine) {
      // Set loading state for splitting
      setIsSplittingLine(true);
      setSplittingLineId(segmentationName);

      // Split the line
      const splitLine = async () => {
        try {
          // Using simplified parameters as requested
          const firstSegmentParams = {
            id: lineId,
            endX: clickedX,
          };

          // Create parameters for the second segment (new line)
          const secondSegmentParams = {
            type: selectedRockLineType,
            startX: clickedX,
            startY: line.startY - startY,
            endX: line.endX,
            endY: line.endY - startY,
            depthFrom: line.depthFrom, // These will be calculated by the server
            depthTo: line.depthTo, // These will be calculated by the server
            imageCropId: data.id,
            rowIndex: imageIndex, // Add rowIndex field
          };

          // Update the first segment (original line)
          const updateResult = await recoveryRequest.updateRockLine(
            firstSegmentParams
          );
          if (updateResult.state === RequestState.error) {
            toast.error(
              updateResult.message || "Failed to update first segment"
            );
            setIsSplittingLine(false);
            return;
          }

          // Create the second segment as a new line
          const createResult = await recoveryRequest.createRockLine(
            secondSegmentParams
          );

          if (createResult.state === RequestState.error) {
            toast.error(
              createResult.message || "Failed to create second segment"
            );
            setIsSplittingLine(false);
            return;
          }

          // Update dragPositions for the updated line and the new line
          setDragPositions((prev) => ({
            ...prev,
            [`line-${lineId}-start`]: line.startX,
            [`line-${lineId}-end`]: clickedX,
            // For the new line (using the data from the response which should have the new ID)
            [`line-${createResult.data}-start`]: clickedX,
            [`line-${createResult.data}-end`]: line.endX,
          }));

          // Refresh image data after successful split
          if (refreshImageData) {
            refreshImageData();
            toast.success("Line split successfully and view refreshed", {
              position: "top-center",
              autoClose: 1000,
            });
          } else {
            toast.success("Line split successfully", {
              position: "top-center",
              autoClose: 1000,
            });
          }
        } catch (error) {
          toast.error("Error splitting line", {
            position: "top-center",
            autoClose: 1000,
          });
        } finally {
          setIsSplittingLine(false);
          setSplittingLineId(null);
        }
      };

      splitLine();
    }
  };

  // Add merge lines API function
  const mergeLinesAPI = async (
    leftLineId: number,
    rightLineId: number,
    leftLine: any,
    rightLine: any
  ) => {
    try {
      // Update the left line to extend to the end of the right line
      const updateParams = {
        id: leftLineId,
        endX: rightLine.endX,
      };

      // Call API to update the left line
      const updateResult = await recoveryRequest.updateRockLine(updateParams);

      if (updateResult.state === RequestState.error) {
        toast.error(updateResult.message || "Failed to merge lines");
        dispatch(updateSelectedLine(null));
        return;
      }

      // Delete the right line
      const deleteResult = await recoveryRequest.deleteRockLine(rightLineId);

      if (deleteResult.state === RequestState.error) {
        toast.error(
          deleteResult.message || "Failed to delete second line after merge"
        );
        dispatch(updateSelectedLine(null));
        return;
      }
      // Reset the selected line
      dispatch(updateSelectedLine(null));

      // Refresh image data after successful merge
      if (refreshImageData) {
        refreshImageData();
        toast.success("Lines merged successfully and view refreshed");
      } else {
        toast.success("Lines merged successfully");
      }

      // Update dragPositions for the merged line
      setDragPositions((prev) => {
        const updatedPositions = { ...prev };
        // Remove the right line positions and update the left line's end position
        delete updatedPositions[`line-${rightLineId}-start`];
        delete updatedPositions[`line-${rightLineId}-end`];
        return {
          ...updatedPositions,
          [`line-${leftLineId}-end`]: rightLine.endX,
        };
      });
    } catch (error) {
      toast.error("Error merging lines");

      dispatch(updateSelectedLine(null));
    }
  };

  return (
    <Fragment>
      <Group
        onContextMenu={handleContextMenu}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onClick={handleImageClick} // Keep for desktop clicks
        onMouseMove={handleMouseMove}
        onMouseLeave={() => setMousePosition(null)}
        opacity={uiProps?.opacity ?? 1}
      >
        {/* Add temporary line when adding a rock line */}
        {isAddLine && firstPoint && firstPoint.rowIndex === imageIndex && (
          <Line
            points={[
              firstPoint.x,
              firstPoint.y,
              // Draw to current mouse position if available, otherwise just to edge
              mousePosition ? mousePosition.x : data.width,
              firstPoint.y,
            ]}
            stroke="yellow"
            strokeWidth={3}
            dash={[10, 5]} // Dashed line
          />
        )}
        <Image
          name="loggingImageRow"
          image={image}
          x={0}
          y={startY}
          width={data?.width}
          height={data?.height}
          ref={imageRef}
        />
        {startPointLine && startPointLine.rowIndex === imageIndex && (
          <Line
            points={[
              startPointLine.x,
              startY,
              startPointLine.x,
              startY + data.height,
            ]}
            stroke="yellow"
            strokeWidth={3}
            dash={[10, 5]}
          />
        )}
        {data.isShowText && (
          <Text
            x={-250}
            y={startY}
            text={depthFrom.toString()}
            fill="black"
            fontFamily="sans-serif"
            fontSize={75}
            perfectDrawEnabled={false}
            onClick={(e) => (e.cancelBubble = true)}
          />
        )}

        {data.isShowText && (
          <Text
            x={data?.width + 50}
            y={startY}
            text={depthTo.toString()}
            fill="black"
            fontFamily="sans-serif"
            fontSize={75}
            perfectDrawEnabled={false}
            onClick={(e) => (e.cancelBubble = true)}
          />
        )}

        {isShowOCR &&
          textOCRs.map((data: any, index: number) => {
            if (data?.type !== "Wooden" && data?.type !== "wooden") return null;
            if (data?.text === "59.90") {
              console.log(
                "data?.x - coordinate[index]: ",
                data?.x - (coordinate?.[index] || 0)
              );
            }
            return (
              <Group
                ref={hbox}
                key={index}
                x={data?.x - (coordinate?.[index] || 0)}
                y={startY - Math.round(120 * (ocrSizing.rectHeight / 160))}
                draggable
                onClick={(event) => {
                  event.cancelBubble = true;
                  setModalState({ isOpen: true, data: data });
                }}
                onDragEnd={(e) => {
                  if (e.target.x() < 0) {
                    toast.error("OCR cannot be outside the image");
                    return;
                  }
                  onChange({
                    ...data,
                    x: e.target.x() + (coordinate?.[index] || 0),
                    y: e.target.y(),
                  });
                  dispatch(updateToggleUpdateOCR());
                }}
                dragBoundFunc={(pos) => {
                  return {
                    x: pos.x,
                    y: hbox?.current?.absolutePosition().y,
                  };
                }}
              >
                <Rect
                  x={0}
                  y={ocrSizing.rectYOffset}
                  width={
                    String(data?.text).length * ocrSizing.charWidthMultiplier
                  }
                  height={ocrSizing.rectHeight}
                  fill={"#2D669B"}
                  strokeWidth={ocrSizing.strokeWidth}
                  stroke="black"
                />
                <Text
                  x={ocrSizing.textXOffset}
                  y={ocrSizing.textYOffset}
                  fill="white"
                  fontFamily="Visby"
                  fontSize={ocrSizing.fontSize}
                  text={data?.text}
                  height={ocrSizing.textHeight}
                />
              </Group>
            );
          })}

        {isCalculating && (
          <Group>
            <Circle
              x={loadingPosition.x}
              y={loadingPosition.y}
              radius={35}
              fill="#FFD700"
              opacity={0.8}
            />
            <Circle
              x={loadingPosition.x}
              y={loadingPosition.y}
              radius={55}
              stroke="#FFD700"
              strokeWidth={6}
              opacity={0.5}
            />
            <Text
              x={loadingPosition.x - 70}
              y={loadingPosition.y + 70}
              text="Calculating..."
              fontSize={20}
              fontStyle="bold"
              fill="#FFD700"
            />
          </Group>
        )}
        {loggingSuiteMode === "Geology" &&
          lines.map((line, index) => {
            if (data.id !== line.data.id) return;
            return (
              <Group key={index}>
                <Line
                  points={[
                    line.data.x,
                    startY,
                    line.data.x,
                    startY + data.height,
                  ]}
                  stroke={line.isSave ? "blue" : "yellow"}
                  strokeWidth={25}
                />
              </Group>
            );
          })}
        {loggingSuiteMode === "Geotech" &&
          !isLoadingGeotechData &&
          isPoint &&
          geotechDataPoint.map((line, index) => {
            if (data.id !== line.id) return;

            return (
              <Group key={index}>
                <Line
                  points={[line.x, startY, line.x, startY + data.height]}
                  stroke="red"
                  strokeWidth={25}
                />

                <Html groupProps={{ x: line.x - 50, y: startY - 150 }}>
                  <IconPickerItem
                    value={structure?.icon ?? "FaUsers"}
                    size={120}
                    color={structure?.textColor ?? "black"}
                  />
                </Html>
              </Group>
            );
          })}
        {loggingSuiteMode === "Geotech" &&
          !isLoadingGeotechData &&
          isInterval &&
          geotechDataInterval.map((line, index) => {
            if (data.id !== line.id) return;
            return (
              <Group key={index}>
                <Line
                  points={[line.x, startY - 50, line.xTo, startY - 50]}
                  stroke="red"
                  strokeWidth={3}
                />
                <Circle x={line.x} y={startY - 50} radius={15} fill="blue" />
                <Circle x={line.xTo} y={startY - 50} radius={15} fill="blue" />
              </Group>
            );
          })}

        {type === EnumLoggingViewStack.Image &&
          isShowPercentageRecovery &&
          recoveries && (
            <Fragment>
              {/* Filter and group recoveries by their value */}
              {recoveries.map((recovery, index) => {
                const fromDirectOcr = directOCRdata.find((ocr) => {
                  return (
                    (ocr.type === "wooden" || ocr.type === "Wooden") &&
                    ((ocr.id === recovery.fromOcrId &&
                      ocr.rowIndex === imageIndex) ||
                      (Number(ocr.text) === recovery.ocrValueFrom &&
                        ocr.rowIndex === imageIndex))
                  );
                });

                let fromXRelative = fromDirectOcr
                  ? fromDirectOcr.x - data.x
                  : 0;

                const toDirectOcr = directOCRdata.find((ocr) => {
                  return (
                    (ocr.type === "wooden" || ocr.type === "Wooden") &&
                    ((ocr.id === recovery.toOcrId &&
                      ocr.rowIndex === imageIndex) ||
                      (Number(ocr.text) === recovery.ocrValueTo &&
                        ocr.rowIndex === imageIndex))
                  );
                });
                let toXRelative = toDirectOcr
                  ? toDirectOcr.x - data.x
                  : data.width;

                // if it doesnot have segmentation before from OCR block or after OCR block, hide the recovery
                // Calculate width based on the difference between positions
                const barWidth = toXRelative - fromXRelative;

                const resultText = `Int: ${recovery.depthInterval.toFixed(
                  2
                )}m Rec: ${recovery.recovery.toFixed(2)}%`;

                // If the row have OCR results, the result text will sit next to the OCR text, otherwise it will be in center
                const x = fromDirectOcr
                  ? fromXRelative +
                    String(fromDirectOcr?.text).length *
                      ocrSizing.charWidthMultiplier +
                    100
                  : fromXRelative + barWidth / 2 - resultText.length * 16;

                return (
                  <Group
                    key={`recovery-${index}`}
                    onClick={(e) => (e.cancelBubble = true)}
                  >
                    {/* Recovery information text */}
                    <Text
                      x={x}
                      y={startY - 200}
                      text={resultText}
                      fontSize={100}
                      fill="black"
                    />
                  </Group>
                );
              })}
            </Fragment>
          )}
        {isShowSegmentation && (
          <Group>
            {transformedSegmentations.map((segmentation) => (
              <Group key={segmentation.name}>
                <SegmentationPoint
                  segmentations={segmentation}
                  isEnableFill={
                    !isMergeLine && !isSplitLine && !isAddLine && !isDeleteLine
                  }
                />
              </Group>
            ))}

            {segmentationLines && segmentationLines?.length > 0 && (
              <>
                {/* Draw line between first two intersection points */}
                {segmentationLines.map((line, lineIndex) => {
                  if (line.id === 421) {
                  }
                  return (
                    <Fragment key={`line-${lineIndex}-${line.id}-${line.endX}`}>
                      <Line
                        points={[
                          dragPositions[`line-${line.id}-start`] ??
                            (typeof line === "object" && "startX" in line
                              ? line.startX
                              : Array.isArray(line)
                              ? line[0]
                              : 0),
                          typeof line === "object" && "startY" in line
                            ? line.startY
                            : Array.isArray(line) && line.length > 1
                            ? line[1]
                            : 0,
                          dragPositions[`line-${line.id}-end`] ??
                            (typeof line === "object" && "endX" in line
                              ? line.endX
                              : Array.isArray(line)
                              ? line[0]
                              : 0),
                          typeof line === "object" && "endY" in line
                            ? line.endY
                            : Array.isArray(line) && line.length > 1
                            ? line[1]
                            : 0,
                        ]}
                        stroke={
                          isSplittingLine &&
                          splittingLineId === `line-${line.id}`
                            ? "#FFD700" // Gold color for splitting state
                            : selectedLine &&
                              selectedLine.id === line.id &&
                              (isMergeLine || isDeleteLine)
                            ? isMergeLine
                              ? "#FF1493" // Pink for selected line in merge mode
                              : "#FF0000" // Red for selected line in delete mode
                            : hoveredLineId === `line-${line.id}` && isSplitLine
                            ? "#ff4500" // Orange-red for hover in split mode
                            : hoveredLineId === `line-${line.id}` && isMergeLine
                            ? "#ff4500" // Orange-red for hover in merge mode
                            : hoveredLineId === `line-${line.id}` &&
                              isDeleteLine
                            ? "#FF0000" // Red for hover in delete mode
                            : "#87CEFA" // Default light blue
                        }
                        strokeWidth={
                          (isSplittingLine &&
                            splittingLineId === `line-${line.id}`) ||
                          (hoveredLineId === `line-${line.id}` &&
                            (isSplitLine || isMergeLine || isDeleteLine)) ||
                          (selectedLine &&
                            selectedLine.id === line.id &&
                            (isMergeLine || isDeleteLine))
                            ? 24 // Wider when hovering or splitting
                            : 16 // Default width
                        }
                        onClick={(e) => {
                          handleLineClick(e, `line-${line.id}`, line.id, line);
                        }}
                        onMouseEnter={() => {
                          if (isSplitLine || isMergeLine || isDeleteLine) {
                            setHoveredLineId(`line-${line.id}`);
                          }
                        }}
                        onMouseLeave={() => {
                          setHoveredLineId(null);
                        }}
                        onTap={(e: any) => {
                          (isSplitLine || isMergeLine || isDeleteLine) &&
                            handleLineClick(
                              e as KonvaEventObject<MouseEvent>,
                              `line-${line.id}`,
                              line.id,
                              line
                            );
                        }}
                      />

                      {/* Draw draggable circles at intersection points */}
                      {(() => {
                        const pointDragPosition =
                          dragPositions[`line-${line.id}-start`] ??
                          (typeof line === "object" && "startX" in line
                            ? line.startX
                            : Array.isArray(line)
                            ? line[0]
                            : 0);

                        const endPointDragPosition =
                          dragPositions[`line-${line.id}-end`] ??
                          (typeof line === "object" && "endX" in line
                            ? line.endX
                            : Array.isArray(line)
                            ? line[0]
                            : 0);

                        return (
                          <Fragment key={`intersection-${lineIndex}`}>
                            {/* Start point circle */}
                            <DraggableIntersectionPoint
                              key={`intersection-start-${line.id}`}
                              x={pointDragPosition}
                              color={getLineColor(line.id)}
                              y={
                                typeof line === "object" && "startY" in line
                                  ? line.startY
                                  : Array.isArray(line) && line.length > 1
                                  ? line[1]
                                  : 0
                              }
                              index={lineIndex}
                              onDragEnd={(idx, newX) =>
                                handleIntersectionDragEnd(
                                  idx,
                                  newX,
                                  line.id,
                                  false
                                )
                              }
                            />
                            {/* End point circle */}
                            <DraggableIntersectionPoint
                              key={`intersection-end-${line.id}`}
                              x={endPointDragPosition}
                              color={getLineColor(line.id)}
                              y={
                                typeof line === "object" && "endY" in line
                                  ? line.endY
                                  : Array.isArray(line) && line.length > 1
                                  ? line[1]
                                  : 0
                              }
                              index={lineIndex}
                              onDragEnd={(idx, newX) =>
                                handleIntersectionDragEnd(
                                  idx,
                                  newX,
                                  line.id,
                                  true
                                )
                              }
                            />
                          </Fragment>
                        );
                      })()}
                    </Fragment>
                  );
                })}
              </>
            )}
          </Group>
        )}
      </Group>

      {/* Render the first point marker in a separate group at the top level */}
      {isAddLine && firstPoint && firstPoint.rowIndex === imageIndex && (
        <Group>
          <Circle
            x={firstPoint.x}
            y={firstPoint.y}
            radius={20} // Larger radius for visibility
            fill="#FFD700" // Gold color for the first point
            stroke="#FF0000" // Bright red border
            strokeWidth={4}
            opacity={1}
            perfectDrawEnabled={false}
            listening={false}
            shadowBlur={10}
            shadowColor="#000000"
            shadowOpacity={0.6}
          />
        </Group>
      )}

      {/* Segmentation Layer - Rendered outside main Group to ensure it's always on top */}

      {contextMenu.visible && (
        <Group x={contextMenu.x} y={contextMenu.y} name="loggingContextMenu">
          {/* Background rectangle for the menu */}
          <Rect width={400} height={130} fill="#F0F0F0" shadowBlur={2} />
          {/* Text items for the menu */}
          <Text
            text="View image"
            x={15}
            y={35}
            width={350}
            height={130}
            fontSize={65}
            fill="black"
            onClick={handlePreviewImage}
          />
        </Group>
      )}
    </Fragment>
  );
}
