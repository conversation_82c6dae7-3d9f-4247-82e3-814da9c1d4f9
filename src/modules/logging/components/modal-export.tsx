import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { ModalCommon } from "@/components/common/modal-common";
import { useGetListDrillhole } from "@/modules/drillhole/hooks/useGetListDrillHole.hook";
import { Select } from "antd";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import recoveryRequest from "../api/recovery.api";
import { ExportType } from "@/modules/logging-data/components/types/export.enum";

export const ModalExport = ({
  modalState,
  setModalState,
}: {
  modalState: any;
  setModalState: any;
}) => {
  const { data: drillholeList, loading, request } = useGetListDrillhole();
  const [keyword, setKeyword] = useState("");
  useEffect(() => {
    request({
      keyword,
      skipCount: 0,
      maxResultCount: 1000,
    });
  }, [keyword]);
  const drillholeDetail = useAppSelector((state) => state.drillHole.detail);
  const [value, setValue] = useState<any>([drillholeDetail?.id]);
  const globalProjectId = useAppSelector(
    (state) => state.user.userInfo.projectId
  );
  const [loadingExport, setLoadingExport] = useState(false);
  return (
    <ModalCommon
      title="Export"
      open={modalState}
      onCancel={() => setModalState(false)}
      centered
      onOk={async () => {
        setLoadingExport(true);
        const res = await recoveryRequest.exportData({
          projectId: globalProjectId as number,
          drillHoleIds: value,
          exportType: ExportType.Calculations,
        });

        if (res.state === "success") {
          setLoadingExport(false);
          toast.success("Export success");
          setModalState(false);
          window.open(res.data ?? "", "_blank");
        } else {
          setLoadingExport(false);
          toast.error("Export failed");
        }
      }}
      confirmLoading={loadingExport}
      okText="Export"
      cancelText="Cancel"
    >
      <div className="flex flex-col gap-2">
        <p className="font-medium">Drillhole</p>
        <Select
          value={value}
          onChange={(value) => setValue(value)}
          allowClear
          showSearch
          placeholder="Select drillhole"
          options={drillholeList?.map((item) => ({
            label: item.name,
            value: item.id,
          }))}
          loading={loading}
          onSearch={(value) => setKeyword(value)}
          filterOption={false}
          mode="multiple"
        />
      </div>
    </ModalCommon>
  );
};
