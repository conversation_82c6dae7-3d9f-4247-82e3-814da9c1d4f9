"use client";
import { RequestState } from "@/common/configs/app.contants";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import dhCalculationRequest from "@/modules/dh-calculation/api/dh-calculation.api";
import {
  DeleteOutlined,
  LoadingOutlined,
  MergeOutlined,
  PlusOutlined,
  SplitCellsOutlined,
} from "@ant-design/icons";
import { IconPentagon, IconRuler, IconRefresh } from "@tabler/icons-react";
import { Button, Dropdown, Modal, Popover, Tooltip } from "antd";
import { Layer as KonvaLayer } from "konva/lib/Layer";
import { KonvaEventObject } from "konva/lib/Node";
import { Stage as KonvaStage } from "konva/lib/Stage";
import { memo, useEffect, useMemo, useRef, useState } from "react";
import { FaPlus } from "react-icons/fa6";
import { GrPowerReset } from "react-icons/gr";
import { RiCharacterRecognitionLine } from "react-icons/ri";
import { SlCalculator } from "react-icons/sl";
import { TbFileTextAi } from "react-icons/tb";
import { Group, Layer, Line, Rect, Stage, Text } from "react-konva";
import { toast } from "react-toastify";
import { AutoSizer } from "react-virtualized";
import recoveryRequest from "../api/recovery.api";
import dataEntryRequest from "../api/data-entry.api";
import {
  ClickDepthData,
  LoggingViewData,
  LoggingViewStack,
} from "../model/dtos/logging.config";
import { EnumLoggingViewStack } from "../model/enum/logging.enum";
import {
  updateHasShownMeasureInstructions,
  updateImageGap,
  updateIsAddLine,
  updateIsDeleteLine,
  updateIsMergeLine,
  updateIsShowPercentageRecovery,
  updateIsShowSegmentation,
  updateIsSplitLine,
  updateLoggingContextMenu,
  updateRecoveries,
  updateTrayDepths,
} from "../redux/loggingSlice";
import {
  ApiError,
  ModalState,
  OCRResultItem,
  Recovery,
} from "../types/logging.types";
import AddOcrModal from "./add-ocr-modal";
import { ImageRowLogging } from "./image-row-logging";
import { ModalEditDepth } from "./modal-edit-depth";
import { CiShare2 } from "react-icons/ci";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";

interface LoggingStageProps {
  loggingViewStacks: LoggingViewStack<LoggingViewData>[];
  directOCRdata: OCRResultItem[];
  onChangeText: (data: OCRResultItem, value: string) => void;
  onEnterChangeText: (data: OCRResultItem, value: string) => void;
  onDblClickText: (e: KonvaEventObject<MouseEvent>) => void;
  onChange: (e: KonvaEventObject<MouseEvent>) => void;
  directOCRdataRaw: OCRResultItem[];
  image: {
    id: string | number;
    [key: string]: any;
  };
  setDirectOCRdata: (data: OCRResultItem[]) => void;
  setIsOpenModalOCRList: (isOpen: boolean) => void;
  refreshImageData: () => void;
  fetchRecoveries: () => void;
  onDeleteOcr: (id: string) => void;
  refreshLoggingBarData?: () => void;
}
const AutoSizerTmp: any = AutoSizer;

function LoggingStage({
  loggingViewStacks,
  directOCRdata,
  onChange,
  onChangeText,
  onDblClickText,
  onEnterChangeText,
  image,
  setDirectOCRdata,
  setIsOpenModalOCRList,
  refreshImageData,
  onDeleteOcr,
  refreshLoggingBarData,
}: LoggingStageProps) {
  const searchParams = useSearchParams();
  const queries: any = {};
  for (const [key, value] of searchParams.entries()) {
    const arrayValues = searchParams.getAll(key);
    queries[key] = arrayValues.length > 1 ? arrayValues : value;
  }
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );
  const globalProspectId = useAppSelector(
    (state) => state.user?.userInfo?.prospectId
  );

  const router = useRouter();
  const scaleBy = 1.1;
  const dispatch = useAppDispatch();
  const contextMenu = useAppSelector((state) => state.logging.contextMenu);
  const imageDepthFrom = useAppSelector((state) => state.logging.depthFrom);
  const imageDepthTo = useAppSelector((state) => state.logging.depthTo);
  const imageRows = useAppSelector((state) => state.logging.imageRows);

  // Touch zoom state
  const [lastCenter, setLastCenter] = useState<{ x: number; y: number } | null>(
    null
  );
  const [lastDist, setLastDist] = useState<number>(0);
  const [isZooming, setIsZooming] = useState(false);
  const touchStartTime = useRef<number>(0);
  const initialTouchCenterRef = useRef<{ x: number; y: number } | null>(null);

  // Helper functions for touch handling
  const getDistance = (
    p1: { x: number; y: number },
    p2: { x: number; y: number }
  ) => {
    return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
  };

  const getCenter = (
    p1: { x: number; y: number },
    p2: { x: number; y: number }
  ) => {
    return {
      x: (p1.x + p2.x) / 2,
      y: (p1.y + p2.y) / 2,
    };
  };

  const handleTouchStart = (e: KonvaEventObject<TouchEvent>) => {
    e.evt.preventDefault();
    const touches = e.evt.touches;
    touchStartTime.current = Date.now();

    if (touches.length === 2) {
      setIsZooming(true);
      const stage = stageRef.current;
      if (!stage) return;

      const p1 = { x: touches[0].clientX, y: touches[0].clientY };
      const p2 = { x: touches[1].clientX, y: touches[1].clientY };

      const center = getCenter(p1, p2);
      const dist = getDistance(p1, p2);

      const stageBox = stage.container().getBoundingClientRect();
      const stagePoint = {
        x: center.x - stageBox.left,
        y: center.y - stageBox.top,
      };

      const oldScale = stage.scaleX();
      const centerPointTo = {
        x: (stagePoint.x - stage.x()) / oldScale,
        y: (stagePoint.y - stage.y()) / oldScale,
      };

      initialTouchCenterRef.current = centerPointTo;
      setLastCenter(center);
      setLastDist(dist);
    } else if (touches.length === 1) {
      setIsZooming(false);
    }
  };

  const handleTouchMove = (e: KonvaEventObject<TouchEvent>) => {
    e.evt.preventDefault();
    const touches = e.evt.touches;

    if (touches.length !== 2) return;

    const stage = stageRef.current;
    if (!stage || !lastCenter || !lastDist || !initialTouchCenterRef.current)
      return;

    const p1 = { x: touches[0].clientX, y: touches[0].clientY };
    const p2 = { x: touches[1].clientX, y: touches[1].clientY };

    const dist = getDistance(p1, p2);
    const currentScale = stage.scaleX();
    const currentCenter = getCenter(p1, p2);
    const stageBox = stage.container().getBoundingClientRect();
    const stagePoint = {
      x: currentCenter.x - stageBox.left,
      y: currentCenter.y - stageBox.top,
    };

    const scaleFactor = Math.pow(dist / lastDist, 1.2);
    const zoomSpeed = 1.03;
    const targetScale = Math.min(
      Math.max(currentScale * scaleFactor * zoomSpeed, 0.03),
      10
    );

    stage.scale({ x: targetScale, y: targetScale });

    const newPos = {
      x: stagePoint.x - initialTouchCenterRef.current.x * targetScale,
      y: stagePoint.y - initialTouchCenterRef.current.y * targetScale,
    };

    stage.position(newPos);
    stage.batchDraw();

    setLastDist(dist);
    setLastCenter(currentCenter);
    setIsZooming(true);
  };

  const handleTouchEnd = (e: KonvaEventObject<TouchEvent>) => {
    e.evt.preventDefault();
    if (e.evt.touches.length < 2) {
      initialTouchCenterRef.current = null;
      setLastCenter(null);
      setLastDist(0);
    }

    if (e.evt.touches.length === 0) {
      setIsZooming(false);
      touchStartTime.current = 0;
    }
  };

  const isShowSegmentation = useAppSelector(
    (state) => state.logging.isShowSegmentation
  );
  const isSplitLine = useAppSelector((state) => state.logging.isSplitLine);
  const isMergeLine = useAppSelector((state) => state.logging.isMergeLine);
  const isAddLine = useAppSelector((state) => state.logging.isAddLine);
  const isDeleteLine = useAppSelector((state) => state.logging.isDeleteLine);
  const selectedDrillhole = useAppSelector(
    (state) => state.logging.selectedDrillHole
  );
  const hasShownMeasureInstructions = useAppSelector(
    (state) => state.logging.hasShownMeasureInstructions
  );
  const stageRef = useRef<KonvaStage>(null);
  const layerRef = useRef<KonvaLayer>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const initialScale = useRef<number>();

  const [isShowOCR, setIsShowOCR] = useState(false);

  const handleClickStage = (event: KonvaEventObject<MouseEvent>) => {
    if (event.target.name() !== "loggingImageRow" && contextMenu.visible) {
      dispatch(
        updateLoggingContextMenu({
          visible: false,
          isRightClick: false,
        })
      );
    }
  };

  const handleMeasurePoint = (point: {
    x: number;
    depth: number;
    rowIndex: number;
  }) => {
    if (!measurePoints.start) {
      setMeasurePoints({
        start: point,
      });
      setStartPointLine({ x: point.x, rowIndex: point.rowIndex });
      toast.info("First point set. Click to set end point.");
    } else {
      setMeasurePoints((prev) => ({
        ...prev,
        end: point,
      }));
      setStartPointLine(null);
      setIsMeasuring(false);
      setIsMeasureModalVisible(true);
    }
  };

  const handleOnWheelStage = (e: KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault();
    const stage = stageRef.current;
    const layer = layerRef.current;
    if (!layer || !stage) return;
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();
    if (!pointer) return;
    let mousePointTo = {
      x: pointer.x / oldScale - stage.x() / oldScale,
      y: pointer.y / oldScale - stage.y() / oldScale,
    };
    let direction = e.evt.deltaY > 0 ? -1 : 1;
    if (e.evt.ctrlKey) {
      direction = -direction;
    }
    let newScale = direction > 0 ? oldScale * scaleBy : oldScale / scaleBy;
    if (newScale < 0.03) return;
    stage.scale({ x: newScale, y: newScale });
    stage.scale({ x: newScale, y: newScale });
    let newPos = {
      x: -(mousePointTo.x - pointer.x / newScale) * newScale,
      y: -(mousePointTo.y - pointer.y / newScale) * newScale,
    };
    stage.position(newPos);
    stage.batchDraw();
  };

  const considerWidth = useMemo(() => {
    const maxWidth = Math.max(
      ...loggingViewStacks
        .filter((stack) => stack.type === EnumLoggingViewStack.Image)
        .map((stack) => stack.data.width || 0)
    );
    return maxWidth;
  }, [loggingViewStacks]);

  useEffect(() => {
    // Calculate initial scale of stage base on image width and screen width
    if (containerRef.current && considerWidth) {
      const containerWidth = containerRef.current.clientWidth;
      // Calculate scale but ensure it's not too small
      let scale = (containerWidth - 200) / considerWidth;
      scale = Math.max(scale, 0.2); // Ensure a minimum scale
      stageRef.current?.scale({ x: scale, y: scale });
      stageRef.current?.position({ x: 60, y: 0 }); // Set a consistent initial position
      stageRef.current?.batchDraw();

      initialScale.current = scale;
    }
  }, []);

  const resetRoom = () => {
    stageRef.current?.scale({
      x: initialScale.current ?? 1.0, // Using 1.0 as a default scale
      y: initialScale.current ?? 1.0,
    });
    stageRef.current?.position({ x: 60, y: 0 });
    stageRef.current?.batchDraw();
  };

  // This use for get clicked depth based on result of OCR
  const clickedDepthData: ClickDepthData[] = useMemo(() => {
    const standardImages = loggingViewStacks.filter(
      (view) => view.type === EnumLoggingViewStack.Image
    );

    const depthDatas: any = [];
    const considerDepthDatas = [
      {
        rowIndex: 0,
        text: imageDepthFrom,
        x: 0,
      },
      ...directOCRdata,
      {
        rowIndex: standardImages.length,
        text: imageDepthTo,
        x: standardImages[standardImages.length - 1]?.data?.width ?? 0,
      },
    ];

    considerDepthDatas.forEach((ocr) => {
      // Get current image data safely with optional chaining and fallback
      const currentImage = standardImages[ocr.rowIndex];
      const relativeStartX = currentImage?.data?.relativeStartX ?? 0;

      depthDatas.push({
        relativeX: relativeStartX + (ocr.x ?? 0),
        depth: ocr.text || "",
      });
    });
    return depthDatas;
  }, [loggingViewStacks, directOCRdata]);
  const [modalState, setModalState] = useState<ModalState>({
    isOpen: false,
    data: undefined,
  });

  const [stateAddOcr, setStateAddOcr] = useState<{
    isOpen: boolean;
    x: number;
    y: number;
    rowIndex: number;
  }>({
    isOpen: false,
    x: 0,
    y: 0,
    rowIndex: 0,
  });

  const [addOCR, setAddOCR] = useState(false);
  const [isCalculating, setIsCalculating] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isCalculatingLoggingBar, setIsCalculatingLoggingBar] = useState(false);
  const recoveries = useAppSelector((state) => state.logging.recoveries);
  const [isMeasuring, setIsMeasuring] = useState(false);
  const [measurePoints, setMeasurePoints] = useState<{
    start?: { x: number; depth: number };
    end?: { x: number; depth: number };
  }>({});
  const [isMeasureModalVisible, setIsMeasureModalVisible] = useState(false);
  const [startPointLine, setStartPointLine] = useState<{
    x: number;
    rowIndex: number;
  } | null>(null);

  const considerRecovery = useMemo(
    () =>
      (recoveries ?? [])?.filter(
        (recovery) =>
          (typeof imageDepthFrom === "number" &&
            typeof imageDepthTo === "number" &&
            // Recovery start point intersects with image depth range
            ((recovery.ocrValueFrom >= imageDepthFrom &&
              recovery.ocrValueFrom < imageDepthTo) ||
              // Recovery end point intersects with image depth range
              (recovery.ocrValueTo > imageDepthFrom &&
                recovery.ocrValueTo <= imageDepthTo) ||
              // Recovery encompasses image depth range
              (recovery.ocrValueFrom <= imageDepthFrom &&
                recovery.ocrValueTo >= imageDepthTo))) ||
          directOCRdata.find(
            (ocr) =>
              Number(ocr.text) === recovery.ocrValueFrom ||
              Number(ocr.text) === recovery.ocrValueTo
          )
      ),
    [recoveries, imageDepthFrom, imageDepthTo, directOCRdata]
  );

  const recoveryWithRowDisplays: (Recovery & { rowDisplays: number[] })[] =
    useMemo(() => {
      const imageRowsWithIndex = imageRows.map((imageRow, rowIndex) => {
        return {
          ...imageRow,
          rowIndex: rowIndex,
        };
      });

      return (considerRecovery ?? []).map((recovery) => {
        const rowDisplays = imageRowsWithIndex
          .filter((imageRowWithIndex) => {
            if (recovery.ocrValueTo < imageDepthFrom) return false;
            if (recovery.ocrValueFrom > imageDepthTo) return false;

            const isOCRFromVisible = directOCRdata.find(
              (directOcr) => Number(directOcr.text) === recovery.ocrValueFrom
            );
            const isOCRToVisible = directOCRdata.find(
              (directOcr) => Number(directOcr.text) === recovery.ocrValueTo
            );

            if (
              Number(recovery.ocrValueFrom.toFixed(2)) ===
              Number(imageDepthTo.toFixed(2))
            )
              return false;

            if (
              !isOCRFromVisible &&
              !isOCRToVisible &&
              recovery.ocrValueTo < imageDepthTo
            )
              return false;

            if (!isOCRFromVisible && !isOCRToVisible) return true;

            if (isOCRFromVisible && !isOCRToVisible) {
              return (
                imageRowWithIndex.rowIndex >= recovery.fromRowIndex &&
                imageRowWithIndex.rowIndex >= isOCRFromVisible.rowIndex
              );
            }

            if (!isOCRFromVisible && isOCRToVisible) {
              return (
                imageRowWithIndex.rowIndex <= recovery.toRowIndex &&
                imageRowWithIndex.rowIndex <= isOCRToVisible.rowIndex
              );
            }

            return (
              imageRowWithIndex.rowIndex >= recovery.fromRowIndex &&
              imageRowWithIndex.rowIndex <= recovery.toRowIndex
            );
          })
          .map((imageRowWithIndex) => imageRowWithIndex.rowIndex);
        return {
          ...recovery,
          rowDisplays: rowDisplays,
        };
      });
    }, [
      considerRecovery,
      imageRows,
      directOCRdata,
      imageDepthFrom,
      imageDepthTo,
    ]);

  const drillholeId = useAppSelector(
    (state) => state.logging.selectedDrillHole
  );
  const selectedImageSubtypeId = useAppSelector(
    (state) => state.logging.selectedImageSubtypeId
  );

  // Extract geologySuiteId from URL parameters like the parent component does
  const loggingSuiteIdParams = queries.loggingSuiteId;
  const geologySuiteId = loggingSuiteIdParams?.startsWith("geologySuites")
    ? loggingSuiteIdParams?.split("-")[1]
    : null;
  const handleCalculateRecoveries = async () => {
    if (!selectedDrillhole?.value) {
      toast.error("No drillhole selected");
      return;
    }

    setIsCalculating(true);
    setIsModalVisible(false);

    try {
      const response = await dhCalculationRequest.executeCalculationRqd({
        drillholeId: Number(drillholeId?.value),
      });
      if (response.state !== RequestState.success) {
        toast.error(response.message || "Failed to calculate recoveries");
        return;
      }
      const result = await recoveryRequest.calculateRecovery(
        Number(selectedDrillhole.value)
      );
      if (result.state === RequestState.success) {
        // Calculate tray depth
        const trayDepthResult = await dhCalculationRequest.calculateTrayDepth({
          drillHoleId: Number(drillholeId?.value),
        });

        if (trayDepthResult.state !== RequestState.success) {
          toast.warning(
            "Tray depth calculation failed, but recoveries were calculated"
          );
        } else {
          // Fetch updated tray depth data after calculation
          const trayDepthsResult = await recoveryRequest.getTrayDepthResult({
            drillHoleId: Number(selectedDrillhole.value),
            maxResultCount: 1000,
            skipCount: 0,
          });

          if (
            trayDepthsResult.state === RequestState.success &&
            trayDepthsResult.data?.items
          ) {
            dispatch(updateTrayDepths(trayDepthsResult.data.items));
          }
        }

        // Get recoveries after calculation
        const recoveriesResult = await recoveryRequest.getRecoveryByDrillHole({
          drillHoleId: Number(selectedDrillhole.value),
        });
        if (
          recoveriesResult.state === RequestState.success &&
          recoveriesResult.data?.items
        ) {
          dispatch(updateRecoveries(recoveriesResult.data.items));
          toast.success("Recoveries calculated successfully");
        }
      } else {
        // Show detailed error message if available
        const errorMessage = result.message || "Failed to calculate recoveries";

        toast.error(errorMessage);
      }
    } catch (error) {
      const apiError = error as ApiError;
      const errorMessage =
        apiError.response?.data?.error?.message ??
        apiError.message ??
        "An error occurred while calculating recoveries";

      toast.error(errorMessage);
    } finally {
      setIsCalculating(false);
    }
  };

  const handleCalculateLoggingBar = async () => {
    if (!selectedDrillhole?.value) {
      toast.error("No drillhole selected");
      return;
    }

    if (!geologySuiteId) {
      toast.error("No geology suite selected");
      return;
    }

    if (!selectedImageSubtypeId) {
      toast.error("No image subtype selected");
      return;
    }

    setIsCalculatingLoggingBar(true);

    try {
      const response = await dataEntryRequest.calculateLoggingBar({
        geologySuiteId: Number(geologySuiteId),
        drillHoleId: Number(selectedDrillhole.value),
        imageSubtypeId: Number(selectedImageSubtypeId),
      });

      if (response.state === RequestState.success) {
        toast.success("Logging bar recalculated successfully");
        // Refresh the image data to reflect the changes
        refreshImageData();
        // Refresh logging bar data if geology suite is selected
        if (refreshLoggingBarData) {
          refreshLoggingBarData();
        }
      } else {
        const errorMessage =
          response.message || "Failed to recalculate logging bar";
        toast.error(errorMessage);
      }
    } catch (error) {
      const apiError = error as ApiError;
      const errorMessage =
        apiError.response?.data?.error?.message ??
        apiError.message ??
        "An error occurred while recalculating logging bar";

      toast.error(errorMessage);
    } finally {
      setIsCalculatingLoggingBar(false);
    }
  };

  useEffect(() => {
    if (!isShowSegmentation) {
      dispatch(updateImageGap(200));
      return;
    }

    if (isShowSegmentation && considerRecovery.length > 0) {
      dispatch(updateImageGap(350));
    }
  }, [considerRecovery, isShowSegmentation]);

  return (
    <div
      className="w-full h-[calc(100vh_-_166px)] min-h-[400px] relative"
      ref={containerRef}
    >
      <div className="absolute z-10 top-0 left-0 flex select-none flex-col">
        <Tooltip title="Reset zoom" placement="left">
          <div
            className="p-2 flex items-center justify-center border border-gray-300 hover:bg-gray-100 bg-white rounded-tr-md hover:cursor-pointer"
            onClick={resetRoom}
          >
            <GrPowerReset style={{ width: 20, height: 20 }} />
          </div>
        </Tooltip>
        <Tooltip title="Show OCR List" placement="left">
          <div
            className="p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:bg-gray-100 bg-white hover:cursor-pointer"
            onClick={() => setIsOpenModalOCRList(true)}
          >
            <TbFileTextAi style={{ width: 20, height: 20 }} />
          </div>
        </Tooltip>
        <Tooltip
          title={`${isShowOCR ? "Hide" : "Show"} depths`}
          placement="left"
        >
          <div
            className={`p-2 mt-3 flex items-center justify-center border border-gray-300 hover:cursor-pointer  ${
              isShowOCR
                ? "bg-primary text-white border-primary"
                : "bg-white hover:bg-gray-100"
            }`}
            onClick={() => setIsShowOCR(!isShowOCR)}
          >
            <RiCharacterRecognitionLine style={{ width: 20, height: 20 }} />
          </div>
        </Tooltip>
        {isShowOCR && (
          <Tooltip title={"Add depth"} placement="left">
            <div
              onClick={() => setAddOCR(!addOCR)}
              className={`p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:cursor-pointer  ${
                addOCR
                  ? "bg-primary text-white border-primary border-t border-t-white"
                  : "bg-white hover:bg-gray-100"
              }`}
            >
              <FaPlus style={{ width: 20, height: 20 }} />
            </div>
          </Tooltip>
        )}

        <Tooltip
          title={`${isShowSegmentation ? "Hide" : "Display "} rock segments`}
          placement="left"
        >
          <div
            className={`p-2 mt-3 flex items-center justify-center border border-gray-300 hover:cursor-pointer ${
              isShowSegmentation
                ? "bg-primary text-white border-primary border-t border-t-white"
                : "bg-white hover:bg-gray-100"
            }`}
            onClick={() => {
              dispatch(updateIsShowSegmentation(!isShowSegmentation));
              dispatch(updateIsShowPercentageRecovery(!isShowSegmentation));

              // Reset segmentation function state
              dispatch(updateIsSplitLine(false));
              dispatch(updateIsMergeLine(false));
              dispatch(updateIsAddLine(false));
              dispatch(updateIsDeleteLine(false));
            }}
          >
            <IconPentagon style={{ width: 20, height: 20 }} />
          </div>
        </Tooltip>
        {isShowSegmentation && (
          <Tooltip title={`Split rock line`} placement="left">
            <div
              onClick={() => {
                dispatch(updateIsSplitLine(!isSplitLine));
              }}
              className={`p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:cursor-pointer  ${
                isSplitLine
                  ? "bg-primary text-white border-primary border-t border-t-white"
                  : "bg-white hover:bg-gray-100"
              }`}
            >
              <SplitCellsOutlined width={20} />
            </div>
          </Tooltip>
        )}

        {isShowSegmentation && (
          <Tooltip title={`Add rock line`} placement="left">
            <div
              onClick={() => {
                dispatch(updateIsAddLine(!isAddLine));
                // Turn off other modes when enabling add line mode
              }}
              className={`p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:cursor-pointer  ${
                isAddLine
                  ? "bg-primary text-white border-primary border-t border-t-white"
                  : "bg-white hover:bg-gray-100"
              }`}
            >
              <PlusOutlined />
            </div>
          </Tooltip>
        )}

        {isShowSegmentation && (
          <Tooltip title={`Merge rock line`} placement="left">
            <div
              onClick={() => {
                dispatch(updateIsMergeLine(!isMergeLine));
              }}
              className={`p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:cursor-pointer  ${
                isMergeLine
                  ? "bg-primary text-white border-primary border-t border-t-white"
                  : "bg-white hover:bg-gray-100"
              }`}
            >
              <MergeOutlined width={20} />
            </div>
          </Tooltip>
        )}

        {isShowSegmentation && (
          <Tooltip title={`Delete rock line`} placement="left">
            <div
              onClick={() => {
                dispatch(updateIsDeleteLine(!isDeleteLine));
              }}
              className={`p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:cursor-pointer  ${
                isDeleteLine
                  ? "bg-primary text-white border-primary border-t border-t-white"
                  : "bg-white hover:bg-gray-100"
              }`}
            >
              <DeleteOutlined width={20} />
            </div>
          </Tooltip>
        )}

        <Tooltip
          title={"Calculate Recovery, RQD & Tray Depth"}
          placement="left"
        >
          <div
            onClick={() => setIsModalVisible(true)}
            className={`p-2 mt-3 flex items-center justify-center border border-gray-300 hover:cursor-pointer bg-white hover:bg-gray-100`}
          >
            {isCalculating ? (
              <LoadingOutlined style={{ width: 20, height: 20 }} />
            ) : (
              <SlCalculator style={{ width: 20, height: 20 }} />
            )}
          </div>
        </Tooltip>

        <Tooltip title={"Measure distance"} placement="left">
          <div
            onClick={() => {
              if (isMeasuring) {
                setIsMeasuring(false);
                setStartPointLine(null);
                setMeasurePoints({});
              } else {
                setIsMeasuring(true);
                setMeasurePoints({});
                setIsMeasureModalVisible(true);
              }
            }}
            className={`p-2 flex items-center justify-center border border-gray-300 border-t-0 hover:cursor-pointer ${
              isMeasuring
                ? "bg-primary text-white"
                : "bg-white hover:bg-gray-100"
            }`}
          >
            <IconRuler style={{ width: 20, height: 20 }} />
          </div>
        </Tooltip>

        <Tooltip title={"Recalculate Logging Bar"} placement="left">
          <div
            onClick={handleCalculateLoggingBar}
            className={`p-2 flex items-center justify-center border border-gray-300 border-t-0 rounded-br-md hover:cursor-pointer bg-white hover:bg-gray-100`}
          >
            {isCalculatingLoggingBar ? (
              <LoadingOutlined style={{ width: 20, height: 20 }} />
            ) : (
              <IconRefresh style={{ width: 20, height: 20 }} />
            )}
          </div>
        </Tooltip>
        <div>
          <div className="p-2 mt-3 flex items-center justify-center border border-gray-300 hover:cursor-pointer bg-white hover:bg-gray-100">
            <Dropdown
              placement="bottom"
              menu={{
                items: [
                  {
                    label: (
                      <p
                        className="text-sm"
                        onClick={() => {
                          const params = {
                            projectId: queries.projectId ?? globalProjectId,
                            prospectId: queries.prospectId ?? globalProspectId,
                            drillholeId:
                              queries.drillholeId ?? drillholeId?.value,
                            activeKey: "Image",
                            loggingSuiteId: queries.loggingSuiteId,
                            geotechSuiteId: queries.geotechSuiteId,
                            imageTypeId: queries.imageTypeId,
                            imageSubTypeId: queries.imageSubTypeId,
                          };
                          const url = `${
                            window.location.origin
                          }/logging?${new URLSearchParams(params).toString()}`;
                          navigator.clipboard.writeText(url);
                          toast.success("Link copied to clipboard");
                        }}
                      >
                        Link to Logging Image Page
                      </p>
                    ),
                    key: "share",
                  },
                  {
                    label: (
                      <p
                        className="text-sm"
                        onClick={() => {
                          const params = {
                            projectId: queries.projectId ?? globalProjectId,
                            prospectId: queries.prospectId ?? globalProspectId,
                            drillholeId:
                              queries.drillholeId ?? drillholeId?.value,
                            activeKey: "Visualize",
                            loggingSuiteId: queries.loggingSuiteId,
                            geotechSuiteId: queries.geotechSuiteId,
                            imageTypeId: queries.imageTypeId,
                            imageSubTypeId: queries.imageSubTypeId,
                            loggingViewId: queries.loggingViewId,
                            depthInput: queries.depthInput,
                          };
                          let _param: any = {};
                          // Only add params that have values
                          Object.entries(params).forEach(([key, value]) => {
                            if (value) {
                              _param[key] = value;
                            }
                          });
                          const url = `${
                            window.location.origin
                          }/logging?${new URLSearchParams(_param).toString()}`;
                          navigator.clipboard.writeText(url);
                          toast.success("Link copied to clipboard");
                        }}
                      >
                        Link to Logging Visualize Page
                      </p>
                    ),
                    key: "share-visualize",
                  },
                ],
              }}
            >
              <CiShare2 style={{ width: 20, height: 20 }} />
            </Dropdown>
          </div>
        </div>

        <Modal
          title={
            measurePoints.start ? "Measurement Results" : "Start Measuring"
          }
          open={
            isMeasureModalVisible &&
            ((!hasShownMeasureInstructions && isMeasuring) ||
              (!!measurePoints.start && !!measurePoints.end))
          }
          onOk={() => {
            if (!hasShownMeasureInstructions) {
              dispatch(updateHasShownMeasureInstructions(true));
            }
            setIsMeasureModalVisible(false);
          }}
          onCancel={() => {
            if (!hasShownMeasureInstructions) {
              dispatch(updateHasShownMeasureInstructions(true));
            }
            setIsMeasureModalVisible(false);
          }}
          okText={measurePoints.start ? "Close" : "Got it"}
          cancelButtonProps={{ style: { display: "none" } }}
          maskClosable={false}
        >
          <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
            {!measurePoints.start ? (
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <IconRuler className="text-primary" />
                  <span className="text-lg font-medium text-gray-800">
                    Instructions
                  </span>
                </div>
                <p className="text-gray-600">
                  Click on any image to set your starting point for measurement.
                </p>
                <div className="mt-4 bg-blue-50 p-3 rounded border border-blue-200">
                  <p className="text-sm text-blue-700">
                    Tip: The measurement will show the distance between two
                    points you select.
                  </p>
                </div>
              </div>
            ) : (
              <div className="text-gray-600">
                Start Point: {measurePoints.start.depth.toFixed(2)} m
              </div>
            )}

            {measurePoints.end && (
              <div className="text-gray-600">
                End Point: {measurePoints.end.depth.toFixed(2)} m
              </div>
            )}

            {measurePoints.start && measurePoints.end && (
              <div className="p-2 bg-blue-50 rounded-lg border border-blue-200">
                <div className="text-sm font-bold text-blue-900">
                  Distance:{" "}
                  {Math.abs(
                    measurePoints.end.depth - measurePoints.start.depth
                  ).toFixed(2)}
                  m
                </div>
              </div>
            )}
          </div>
        </Modal>

        <Modal
          title="Confirm Calculations"
          open={isModalVisible}
          onOk={handleCalculateRecoveries}
          onCancel={() => setIsModalVisible(false)}
        >
          <div className="space-y-6 p-4 bg-gray-50 rounded-lg">
            <p className="text-base font-medium text-gray-800">
              Please confirm that you want to calculate recoveries, RQD, and
              tray depth for this drillhole.
            </p>
            <div>
              <p className="text-base text-gray-700 font-medium mb-2">
                Before proceeding, ensure that:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li className="text-gray-600 hover:text-primary transition-colors">
                  OCR values are correctly set
                </li>
                <li className="text-gray-600 hover:text-primary transition-colors">
                  Segmentation is properly defined
                </li>
                <li className="text-gray-600 hover:text-primary transition-colors">
                  Images are properly cropped
                </li>
              </ul>
            </div>
            <div className="bg-yellow-50 p-3 rounded border border-yellow-200">
              <p className="text-sm text-yellow-700">
                Note: This process cannot be undone. Please review carefully
                before proceeding.
              </p>
            </div>
          </div>
        </Modal>

        {/* <Tooltip title={"Save"} placement="left">
          <div
            onClick={handleSaveOCR}
            className="p-2 flex items-center justify-center border-l border-r border-b border-gray-300 rounded-br-md hover:cursor-pointer bg-white hover:bg-gray-100"
          >
            {loadingSaveOCR ? (
              <LoadingOutlined style={{ width: 20, height: 20 }} />
            ) : (
              <RiSave3Line style={{ width: 20, height: 20 }} />
            )}
          </div>
        </Tooltip> */}
      </div>
      {modalState.isOpen && (
        <ModalEditDepth
          modalState={modalState}
          onEnterChangeText={onEnterChangeText}
          setModalState={setModalState}
          setDirectOCRdata={setDirectOCRdata}
          onDeleteOcr={onDeleteOcr}
        />
      )}
      {stateAddOcr.isOpen && (
        <AddOcrModal
          stateAddOcr={stateAddOcr}
          setStateAddOcr={setStateAddOcr}
          setDirectOCRdata={setDirectOCRdata}
          directOCRdata={directOCRdata}
          image={image}
        />
      )}
      <AutoSizerTmp>
        {({ width, height }) => {
          return (
            <Stage
              draggable={!isZooming}
              x={60}
              y={0}
              onWheel={handleOnWheelStage}
              ref={stageRef}
              width={width}
              height={Math.max(height - 20, 300)}
              onClick={handleClickStage}
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
            >
              <Layer ref={layerRef}>
                {(loggingViewStacks ?? []).map((loggingView, index) => {
                  if (
                    [
                      EnumLoggingViewStack.Image,
                      EnumLoggingViewStack.Below,
                      EnumLoggingViewStack.Overlay,
                    ].includes(loggingView.type)
                  ) {
                    return (
                      <ImageRowLogging
                        key={loggingView?.id}
                        addOCR={addOCR}
                        data={loggingView.data}
                        uiProps={loggingView.uiProps}
                        startY={loggingView.startY}
                        type={loggingView.type}
                        directOCRdata={directOCRdata}
                        onChangeText={onChangeText}
                        onDblClickText={onDblClickText}
                        onChange={onChange}
                        isShowOCR={isShowOCR && loggingView?.data?.isShowOCR}
                        image={image}
                        imageIndex={loggingView.index}
                        clickedDepthData={clickedDepthData}
                        setModalState={setModalState}
                        modalState={modalState}
                        setStateAddOcr={setStateAddOcr}
                        stateAddOcr={stateAddOcr}
                        isMeasuring={isMeasuring}
                        onMeasurePoint={handleMeasurePoint}
                        startPointLine={startPointLine}
                        recoveries={recoveryWithRowDisplays.filter((recovery) =>
                          recovery.rowDisplays.includes(loggingView.index)
                        )}
                        refreshImageData={refreshImageData}
                      />
                    );
                  }

                  if (loggingView.type === EnumLoggingViewStack.Point) {
                    return (
                      <Line
                        key={loggingView?.id}
                        y={loggingView.startY}
                        points={loggingView.data?.points?.flatMap((point) => [
                          point.x,
                          point.y,
                        ])}
                        stroke="black"
                        strokeWidth={6}
                        lineJoin="round"
                        lineCap="round"
                        tension={0.1} // smooth the line
                      />
                    );
                  }

                  if (loggingView.type === EnumLoggingViewStack.LoggingBar) {
                    const width = loggingView.data.width || 0;
                    const x = loggingView.data.x || 0;
                    return (
                      <Group key={loggingView?.id}>
                        <Rect
                          x={x}
                          y={loggingView.startY}
                          width={width}
                          height={loggingView.data.height || 20}
                          fill={loggingView.data.color || "#CCCCCC"}
                          stroke="#000"
                          strokeWidth={2}
                          opacity={0.9}
                        />
                        {/* Add text to show depth range if there's enough space */}
                        {/* {width > 60 && (
                          <Text
                            x={x + 2}
                            y={loggingView.startY + 2}
                            text={`${loggingView.data.depthFrom?.toFixed(
                              2
                            )} - ${loggingView.data.depthTo?.toFixed(2)}`}
                            fontSize={75}
                            fill="#fff"
                            fontStyle="bold"
                            width={width - 4}
                            align="center"
                            wrap="none"
                          />
                        )} */}
                      </Group>
                    );
                  }
                  return null;
                })}
              </Layer>
            </Stage>
          );
        }}
      </AutoSizerTmp>
    </div>
  );
}

export default memo(LoggingStage);
