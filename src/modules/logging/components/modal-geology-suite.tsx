import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { InputNumberCommon } from "@/components/common/input-number";
import { FieldType } from "@/modules/geology-suite-field/const/enum";
import { Button, Form } from "antd";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { DataEntryBody } from "../api/data-entry.api";
import { useCreateDataEntry } from "../hooks/useCreateDataEntry";
import { useUpdateDataEntry } from "../hooks/useUpdateDataEntry";
import { RenderField } from "./renderField";
import { updateRefetchLoggingView } from "../redux/loggingSlice";

export interface IModalCreateEntryProps {
  modalState: {
    isOpen: boolean;
    detail?: DataEntryBody;
    type: "create" | "update" | "delete" | "view";
  };
  setModalState: (value: {
    isOpen: boolean;
    detail?: DataEntryBody;
    type: "create" | "update" | "delete" | "view";
  }) => void;
  refetchDataEntry: () => void;
}

export function ModalCreateEntry({
  modalState,
  setModalState,
  refetchDataEntry,
}: IModalCreateEntryProps) {
  const geologySuite = useAppSelector((state) => state.geologySuite.detail);
  let geologySuiteFields = geologySuite?.geologySuiteFields ?? [];
  geologySuiteFields = geologySuiteFields.filter((item) => item?.isActive);
  const dispatch = useAppDispatch();
  const { control, handleSubmit, setValue, getValues, reset, watch } =
    useForm<DataEntryBody>();

  const { request: requestCreateDataEntry, loading: loadingCreateDataEntry } =
    useCreateDataEntry();
  const { detail: selectedDrillhole } = useAppSelector(
    (state) => state.drillHole
  );
  const { detail: selectedGeologySuite } = useAppSelector(
    (state) => state.geologySuite
  );
  const { request: requestUpdateDataEntry, loading: loadingUpdateDataEntry } =
    useUpdateDataEntry();
  const { allLoggings } = useAppSelector((state) => state.logging);
  const onSubmit = (data: any) => {
    const checkIsInvalid = data.dataEntryValues.some((item) => {
      const geologySuiteField = geologySuiteFields.find(
        (field) => field.id === item.geologySuiteFieldId
      );
      if (geologySuiteField?.isMandatory) {
        switch (item.fieldType) {
          case FieldType.Colour:
            if (!item.colourId) {
              toast.error("Colour is required");
              return true;
            }
            break;
          case FieldType.NumberField:
            if (!item.numberValue) {
              toast.error("Number Value is required");
              return true;
            }
            break;
          case FieldType.RockGroup:
            if (!item.rockTypeId) {
              toast.error("Rock Type is required");
              return true;
            }
            break;
          case FieldType.PickList:
            if (!item.pickListItemId) {
              toast.error("Pick List Item is required");
              return true;
            }
            break;
          case FieldType.Description:
            if (!item.description) {
              toast.error("Description is required");
              return true;
            }
            break;
          case FieldType.RockType:
            if (!item.numberValue) {
              toast.error("Number Value is required");
              return true;
            }
            break;
          case FieldType.RockSelect:
            if (!item.rockTypeId || !item.numberValue) {
              toast.error("Rock Type and Number Value are required");
              return true;
            }
            break;
          case FieldType.DateField:
            if (!item.dateValue) {
              toast.error("Date is required");
              return true;
            }
            break;
          default:
            break;
        }
      }
      return false;
    });
    if (checkIsInvalid) {
      return;
    }

    const newDepthFrom = parseFloat(data.depthFrom?.toString() ?? "0");
    const newDepthTo = parseFloat(data.depthTo?.toString() ?? "0");

    if (newDepthFrom >= newDepthTo) {
      toast.error("Depth from must be less than depth to");
      return;
    }

    const hasOverlap = allLoggings.some((entry) => {
      const entryDepthFrom = parseFloat(entry.depthFrom?.toString() ?? "0");
      const entryDepthTo = parseFloat(entry.depthTo?.toString() ?? "0");

      if (modalState.detail && entry.id === modalState.detail.id) {
        return false;
      }

      return (
        (newDepthFrom >= entryDepthFrom && newDepthFrom < entryDepthTo) ||
        (newDepthTo > entryDepthFrom && newDepthTo <= entryDepthTo) ||
        (newDepthFrom <= entryDepthFrom && newDepthTo >= entryDepthTo)
      );
    });

    if (hasOverlap) {
      toast.error("Depth range overlaps with existing entries");
      return;
    }

    if (modalState.detail) {
      requestUpdateDataEntry(
        {
          ...data,
          id: modalState.detail?.id,
          drillholeId: selectedDrillhole?.id,
          geologySuiteId: selectedGeologySuite?.id,
        },
        () => {
          setModalState({ ...modalState, isOpen: false, detail: undefined });
          refetchDataEntry();
          toast.success("Update data entry success");
          dispatch(updateRefetchLoggingView());
        },
        (error) => {
          toast.error(error?.message);
        }
      );
    } else {
      requestCreateDataEntry(
        {
          ...data,
          drillholeId: selectedDrillhole?.id,
          geologySuiteId: selectedGeologySuite?.id,
        },
        (res) => {
          setModalState({ ...modalState, isOpen: false, detail: undefined });
          refetchDataEntry();
          toast.success("Create data entry success");
          dispatch(updateRefetchLoggingView());
        },
        (error) => {
          toast.error(error?.message);
        }
      );
    }
    reset();
  };

  useEffect(() => {
    if (modalState.detail) {
      setValue("depthFrom", modalState.detail?.depthFrom ?? undefined);
      setValue("depthTo", modalState.detail?.depthTo ?? undefined);
      let _dataEntries: any[] = [];

      geologySuiteFields.forEach((field, index) => {
        const isNumberField =
          field?.geologyField?.type === FieldType.NumberField;
        const isRockGroupField =
          field?.geologyField?.type === FieldType.RockGroup;
        const isPickField = field?.geologyField?.type === FieldType.PickList;
        const isDescriptionField =
          field?.geologyField?.type === FieldType.Description;
        const isRockTypeField =
          field?.geologyField?.type === FieldType.RockType;
        const isRockSelectField =
          field?.geologyField?.type === FieldType.RockSelect;
        const isDateField = field?.geologyField?.type === FieldType.DateField;
        const isColourField = field?.geologyField?.type === FieldType.Colour;
        const isRockTreeField =
          field?.geologyField?.type === FieldType.RockTree;
        if (isColourField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id
          );
          _dataEntries.push({
            fieldType: FieldType.Colour,
            geologySuiteFieldId: field.id,
            colourId: item?.colourId,
          });
        } else if (isNumberField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id
          );
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.NumberField,
              geologySuiteFieldId: field.id,
              numberValue: item?.numberValue,
              numberId: field?.geologyField?.number?.id,
            });
          }
        } else if (isRockGroupField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id
          );
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.RockGroup,
              geologySuiteFieldId: field.id,
              rockTypeId: item?.rockTypeId,
            });
          }
        } else if (isPickField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id
          );

          if (item) {
            _dataEntries.push({
              fieldType: FieldType.PickList,
              geologySuiteFieldId: field.id,
              pickListItemId: item?.pickListItemId,
            });
          }
        } else if (isDescriptionField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id
          );
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.Description,
              geologySuiteFieldId: field.id,
              description: item?.description,
            });
          }
        } else if (isRockTypeField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id
          );
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.RockType,
              geologySuiteFieldId: field.id,
              numberValue: item?.numberValue,
              numberId: field?.geologyField?.rockTypeNumber?.number?.id,
            });
          }
        } else if (isRockSelectField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id
          );
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.RockSelect,
              geologySuiteFieldId: field.id,
              rockTypeId: item?.rockTypeId,
              numberValue: item?.numberValue,
              numberId: field?.geologyField?.rockSelectNumber?.number?.id,
            });
          }
        } else if (isDateField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id
          );

          if (item) {
            _dataEntries.push({
              fieldType: FieldType.DateField,
              geologySuiteFieldId: field.id,
              dateValue: item?.dateValue?.toString(),
            });
          }
        } else if (isRockTreeField) {
          const item = modalState?.detail?.dataEntryValues?.find(
            (value) => value.geologysuiteFieldId === field.id
          );
          console.log(item);
          if (item) {
            _dataEntries.push({
              fieldType: FieldType.RockTree,
              geologySuiteFieldId: field.id,
              rockNodeId: item?.rockNodeId,
            });
          }
        }
      });
      setValue("dataEntryValues", _dataEntries);
    } else {
      reset();
      let _dataEntries: any[] = [];
      geologySuiteFields.forEach((field, index) => {
        _dataEntries.push({
          fieldType: field.geologyField?.type,
          geologySuiteFieldId: field.id,
        });
      });
      setValue("dataEntryValues", _dataEntries);
    }
  }, [modalState.detail]);

  const logging = useAppSelector((state) => state.logging);
  const { reviewDepthFrom, reviewDepthTo } = logging;
  useEffect(() => {
    if (!modalState.detail) {
      setValue("depthFrom", reviewDepthFrom ?? undefined);
      setValue("depthTo", reviewDepthTo ?? undefined);
    }
  }, [modalState, reviewDepthFrom, reviewDepthTo]);

  return (
    <Form onFinish={handleSubmit(onSubmit)}>
      <div className="flex flex-col gap-2">
        <p className="font-semibold text-32-32">
          {modalState.detail ? "Update entry" : "Add entry"}
        </p>
        <div className="flex gap-2">
          <p className="font-semibold">Drillhole</p>
          <p>{selectedDrillhole?.name}</p>
        </div>
        <div className="flex gap-2">
          <p className="font-semibold">Geology Suite</p>
          <p>{selectedGeologySuite?.name}</p>
        </div>
        <div className="flex flex-col gap-4 ">
          <InputNumberCommon
            control={control}
            name="depthFrom"
            className="w-24"
            label="Depth From"
            placeholder="Depth From"
            precision={2}
          />
          <InputNumberCommon
            control={control}
            name="depthTo"
            className="w-24"
            label="Depth To"
            placeholder="Depth To"
            precision={2}
          />
        </div>
        <div className="flex flex-col gap-4 max-h-[500px] overflow-y-auto">
          {geologySuiteFields
            .filter((field) => field.isActive)
            .map((field, index) => (
              <div
                key={field.id}
                style={{
                  backgroundColor: field.backgroundColour,
                  color: field.textColour,
                }}
                className="flex flex-col gap-2 rounded-lg border border-black p-2"
              >
                <p className="font-semibold uppercase">
                  {field.name} {field.isMandatory ? "*" : ""}
                </p>
                <RenderField
                  field={field}
                  control={control}
                  setValue={setValue}
                  getValues={getValues}
                  index={index}
                />
              </div>
            ))}
        </div>
        <div className="flex justify-end gap-2 ">
          <Button
            onClick={() => setModalState({ ...modalState, isOpen: false })}
          >
            Cancel
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={loadingUpdateDataEntry || loadingCreateDataEntry}
          >
            {modalState.detail ? "Update" : "Save"}
          </Button>
        </div>
      </div>
    </Form>
  );
}
