import { useState } from "react";
import dataEntryRequest, { DataEntryBody } from "../api/data-entry.api";

export const useGetListDataEntry = () => {
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<DataEntryBody[]>([]);
  const request = async (
    params: {
      GeologySuiteId?: number;
      DrillholeId?: number;
      DepthFrom?: number;
      DepthTo?: number;
      skipCount?: number;
      maxResultCount?: number;
    },
    onSuccess?: Function,
    onError?: Function,
    forceUpdate?: boolean
  ) => {
    setLoading(true);
    const response = await dataEntryRequest.getAllDataEntry({
      ...params,
    });
    if (response?.state === "success") {
      setData(response.data?.items);
      setTotal(response.data?.pagination?.total);
      setLoading(false);
      onSuccess && onSuccess(response.data);
      return response.data;
    } else {
      onError && onError(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data, total };
};
