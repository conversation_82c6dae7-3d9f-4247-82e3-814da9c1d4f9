"use client";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { useGetListDrillhole } from "@/modules/drillhole/hooks/useGetListDrillHole.hook";
import recoveryRequest from "@/modules/logging/api/recovery.api";
import { useGetListProject } from "@/modules/projects/hooks/useGetListProject";
import { useGetListProspect } from "@/modules/prospect/hooks/useGetListProspect";
import { ExportOutlined, LoadingOutlined } from "@ant-design/icons";
import { Radio, Select } from "antd";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { ExportType } from "./types/export.enum";
export function LoggingData() {
  const drillholeDetail = useAppSelector((state) => state.drillHole.detail);
  const globalProject = useAppSelector(
    (state) => state.user.userInfo.projectId
  );
  const globalProspect = useAppSelector(
    (state) => state.user.userInfo.prospectId
  );
  const [projectSelected, setProjectSelected] = useState<any>(globalProject);
  const [prospectSelected, setProspectSelected] = useState<any>(globalProspect);

  const { data: drillholeList, request: requestDrillholeList } =
    useGetListDrillhole();

  const { data: prospectList, request: requestProspectList } =
    useGetListProspect();
  const { data: projectList, request: requestProjectList } =
    useGetListProject();
  const [maxResultCountProject, setMaxResultCountProject] = useState(10);
  const handleScrollProject = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountProject(maxResultCountProject + 10);
    }
  };
  const [keywordLoggingData, setKeywordLoggingData] = useState("");
  const [keywordProject, setKeywordProject] = useState("");
  const [keywordProspect, setKeywordProspect] = useState("");

  useEffect(() => {
    requestDrillholeList({
      keyword: keywordLoggingData,
      maxResultCount: 1000,
      skipCount: 0,
      isActive: true,
      projectIds: projectSelected ? [projectSelected] : undefined,
      prospectIds: prospectSelected ? [prospectSelected] : undefined,
    });
  }, [keywordLoggingData, projectSelected, prospectSelected]);

  useEffect(() => {
    requestProjectList({
      keyword: keywordProject,
      maxResultCount: maxResultCountProject,
      skipCount: 0,
      isActive: true,
    });
  }, [keywordProject, maxResultCountProject]);
  const [maxResultCountProspect, setMaxResultCountProspect] = useState(10);
  const handleScrollProspect = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountProspect(maxResultCountProspect + 10);
    }
  };
  useEffect(() => {
    requestProspectList({
      keyword: keywordProspect,
      maxResultCount: maxResultCountProspect,
      skipCount: 0,
      projectIds: projectSelected ? [projectSelected] : undefined,
      isActive: true,
    });
  }, [keywordProspect, projectSelected, maxResultCountProspect]);
  const [drillholeSelected, setDrillholeSelected] = useState<any>([
    drillholeDetail?.id,
  ]);
  const [radioValue, setRadioValue] = useState<any>(1);
  const [loadingExport, setLoadingExport] = useState(false);
  const router = useRouter();
  const handleExport = async () => {
    setLoadingExport(true);

    if (radioValue === 1) {
      // export project holes
      const response = await recoveryRequest.exportData({
        drillHoleIds: [],
        projectId: projectSelected,
        prospectId: prospectSelected,
        exportType: ExportType.All,
      });
      if (response.state === "success") {
        toast.success("Export success");
        router.push(response.data ?? "");
        setLoadingExport(false);
      } else {
        toast.error("Export failed");
        setLoadingExport(false);
      }
    } else {
      const response = await recoveryRequest.exportData({
        drillHoleIds: drillholeSelected ?? [],
        projectId: projectSelected,
        prospectId: prospectSelected,
        exportType: ExportType.All,
      });
      if (response.state === "success") {
        toast.success("Export success");
        router.push(response.data ?? "");
        setLoadingExport(false);
      } else {
        toast.error("Export failed");
        setLoadingExport(false);
      }
    }
  };
  return (
    <div className="flex flex-col gap-4">
      <p className="text-2xl font-bold">Logging Data</p>
      <Radio.Group
        value={radioValue}
        options={[
          { value: 1, label: "Project Holes" },
          { value: 2, label: "Select Drill holes" },
        ]}
        onChange={(e) => setRadioValue(e.target.value)}
      />
      <div className="flex flex-col gap-2">
        <p className="text-sm font-bold">Project</p>
        <Select
          allowClear
          showSearch
          onPopupScroll={handleScrollProject}
          options={projectList?.map((item) => ({
            label: item.name,
            value: item.id,
          }))}
          placeholder="Select Project"
          className="w-full"
          filterOption={false}
          onSearch={(value) => setKeywordProject(value)}
          value={projectSelected}
          onChange={(value) => {
            setProjectSelected(value);
            setProspectSelected(null);
            setDrillholeSelected(null);
          }}
        />
      </div>
      <div className="flex flex-col gap-2">
        <p className="text-sm font-bold">Prospect</p>
        <Select
          allowClear
          showSearch
          onPopupScroll={handleScrollProspect}
          options={prospectList?.map((item) => ({
            label: item.name,
            value: item.id,
          }))}
          placeholder="Select Prospect"
          className="w-full"
          filterOption={false}
          onSearch={(value) => setKeywordProspect(value)}
          value={prospectSelected}
          onChange={(value) => {
            setProspectSelected(value);
            setDrillholeSelected(null);
          }}
        />
      </div>

      {radioValue === 2 && (
        <div className="flex flex-col gap-2">
          <p className="text-sm font-bold">Drillhole</p>
          <Select
            allowClear
            showSearch
            options={drillholeList?.map((item) => ({
              label: item.name,
              value: item.id,
            }))}
            placeholder="Select Drillhole"
            className="w-full"
            filterOption={false}
            onSearch={(value) => setKeywordLoggingData(value)}
            value={drillholeSelected}
            onChange={(value) => setDrillholeSelected(value)}
            mode="multiple"
          />
        </div>
      )}
      <div className="">
        <button
          onClick={handleExport}
          className="flex items-center gap-2 p-1 bg-[#0F763D] text-white rounded-md hover:bg-[#0F763D]/80"
        >
          {loadingExport ? <LoadingOutlined /> : <ExportOutlined />}
          Export
        </button>
      </div>
    </div>
  );
}
