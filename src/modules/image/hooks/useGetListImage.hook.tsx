import imageRequest from "../api/image.api";
import { ImageQuery } from "../interface/image.interface";

export const useGetListImage = () => {
  async function request(
    params: ImageQuery, // Renamed to rawParams to indicate potential pre-transformation state
    setLoading?: Function,
    onSuccess?: Function,
    onError?: Function
  ) {
    setLoading && setLoading(true);
    // Ensure all parameter keys are in camelCase before sending to the API
    const response = await imageRequest.getListImage({
      ...params,
      maxResultCount: params.maxResultCount ?? 1000,
      skipCount: params.skipCount ?? 0,
    });
    if (response?.status === 200) {
      onSuccess && onSuccess(response.data);
      setLoading && setLoading(false);
      return response.data;
    } else {
      onError && onError(response);
      setLoading && setLoading(false);
    }
  }

  return { request };
};
