import { useState } from "react";
import imageRequest from "../api/image.api";
import { ImageQuery } from "../interface/image.interface";

export const useGetListImage = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>();
  const [total, setTotal] = useState(0);
  async function request(
    params: ImageQuery,
    onSuccess?: Function,
    onError?: Function
  ) {
    setLoading && setLoading(true);
    const response = await imageRequest.getImages({
      ...params,
      maxResultCount: params.maxResultCount ?? 1000,
      skipCount: params.skipCount ?? 0,
    });
    if (response?.state === "success") {
      onSuccess && onSuccess(response.data);
      setData(response.data?.items[0]);
      setTotal(response.data?.pagination.total);
      setLoading && setLoading(false);
      return response.data;
    } else {
      onError && onError(response);
      setLoading && setLoading(false);
    }
  }

  return { request, loading, data, total, setData };
};
