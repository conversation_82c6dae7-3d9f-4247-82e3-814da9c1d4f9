"use client";
import AppGuide from "@/common/@share-components/@shared/AppGuide";
import { RequestState } from "@/common/configs/app.contants";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import AppLoading from "@/components/common/app-loading";
import { ButtonCommon } from "@/components/common/button-common";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import VituralizeList from "@/components/common/vitural-list";
import drillHoleRequest from "@/modules/drillhole/api/drillhole.api";
import { DrillHoleStatus } from "@/modules/drillhole/constants/drillhole.constants";
import { useQueryImageSubType } from "@/modules/image-type/hooks/useGetQueryImageSubType";
import { useQueryImageType } from "@/modules/image-type/hooks/useQueryImageType";
import projectRequest from "@/modules/projects/api/project.api";
import { useGetListProspect } from "@/modules/prospect/hooks/useGetListProspect";
import stepWorkflowsRequest from "@/modules/workflows/api/step-workflow.api";
import { useGetListWorkflows } from "@/modules/workflows/hooks/useGetListWorkflow.hook";
import {
  DeleteOutlined,
  EditOutlined,
  ReloadOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Button,
  Checkbox,
  Form,
  Image,
  Input,
  Popover,
  Select,
  Switch,
  Tooltip,
} from "antd";
import { isEmpty } from "lodash";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { LuView } from "react-icons/lu";
import { MdCheckCircleOutline, MdLightbulbOutline } from "react-icons/md";
import { VscError } from "react-icons/vsc";
import { toast } from "react-toastify";
import { z } from "zod";
import imageRequest from "../api/image.api";
import { imageCategoryOptions } from "../model/enum/images.enum";
import PreviewPolygon from "./canvas/preview-polygon";
import ProgressBar from "./progress-bar";
import RenameImage from "./rename-image";

interface UploadFiles {
  file: any;
  url: string;
  status?: "valid" | "done" | "error";
  description?: string;
  errorMessage?: string;
  errorType?: "errorName";
  reLoading?: boolean;
}

interface FormUploadFiles {
  projectId?: number;
  workflowId?: number;
  prospectId?: number;
  boundingRowId?: number;
  boundingBoxId?: number;
  isUnnamed?: boolean;
  isAiNaming?: boolean;
  isSetDrillHoleName?: boolean;
  setDrillHoleName?: string;
  imageTypeId?: number;
  imageSubtypeId?: number;
  imageCategory?: number;
  // Add this field to track available subtype options for validation
  _imageSubtypeOptions?: any[];
}

export const UploadImageValidation = z
  .object({
    projectId: z.number().optional(),
    workflowId: z.number().optional(),
    prospectId: z.number().optional(),
    boundingRowId: z.number().optional(),
    boundingBoxId: z.number().optional(),
    isUnnamed: z.boolean().optional(),
    isAiNaming: z.boolean().optional(),
    isSetDrillHoleName: z.boolean().optional(),
    setDrillHoleName: z.string().optional(),
    imageTypeId: z.number(),
    imageSubtypeId: z.number().optional(),
    imageCategory: z.number(),
    // Add this field to track available subtype options for validation
    _imageSubtypeOptions: z.array(z.any()).optional(),
  })
  .strict()
  .superRefine(
    (
      {
        isSetDrillHoleName,
        setDrillHoleName,
        imageTypeId,
        imageSubtypeId,
        _imageSubtypeOptions,
      },
      ctx
    ) => {
      if (isSetDrillHoleName && isEmpty(setDrillHoleName)) {
        ctx.addIssue({
          code: "custom",
          message: "Please specific the drillhole name",
          path: ["setDrillHoleName"],
        });
      }

      // Conditional validation for Image Subtype
      if (
        imageTypeId &&
        _imageSubtypeOptions &&
        _imageSubtypeOptions.length > 0
      ) {
        // Image Type is selected AND subtype options are available - make subtype required
        if (!imageSubtypeId) {
          ctx.addIssue({
            code: "custom",
            message: "Please select an image subtype",
            path: ["imageSubtypeId"],
          });
        }
      }
      // When imageTypeId is selected but no subtype options available, imageSubtypeId is optional (no validation needed)
      // When no imageTypeId is selected, imageSubtypeId should remain disabled (handled by UI)
    }
  );

const FILE_NAME_REGEX =
  /^([^_]+)_(\d+(\.\d+)?)_(\d+(\.\d+)?)(?:_.*)?\.[a-zA-Z0-9]+$/;

const UploadImages: React.FC = () => {
  const [uploadType, setUploadType] = useState<"files" | "folder">("folder");

  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );
  const globalProspectId = useAppSelector(
    (state) => state.user.userInfo?.prospectId
  );

  const { data: workflows, request: requestGetListWorkflows } =
    useGetListWorkflows();
  const [isEndWorkflow, setEndWorkflow] = useState(false);
  const [maxResultCountWorkflow, setMaxResultCountWorkflow] = useState(10);
  const [loading, setLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [fileChange, setFileChange] = useState(0);
  const uploadFolderRef = useRef<any>(null);

  const [modalState, setModalState] = useState<boolean>(false);
  const [boundingBoxes, setBoundingBoxes] = useState<{}[]>([]);
  const [boundingRows, setBoundingRows] = useState<{}[]>([]);
  const {
    handleSubmit,
    control,
    watch,
    reset,
    setValue,
    formState: { errors },
  } = useForm<FormUploadFiles>({
    resolver: zodResolver(UploadImageValidation),
    defaultValues: {
      projectId: globalProjectId,
      prospectId: globalProspectId,
      boundingBoxId: undefined,
      boundingRowId: undefined,
      isUnnamed: false,
      imageTypeId: undefined,
      imageSubtypeId: undefined,
      _imageSubtypeOptions: [],
    },
  });
  console.log("errors: ", errors);

  const projectSelected = watch("projectId");
  const [previewImageIndex, setPreviewImageIndex] = useState<number>(-1);
  const [previewImagePolygonUrl, setPreviewPolygonUrl] = useState<
    string | undefined
  >(undefined);

  const [uploadFiles, setUploadFiles] = useState<UploadFiles[]>([]);
  const [stagingFiles, setStagingFiles] = useState<FileList | never[]>();
  const createdDrillholeRef = useRef<Map<string, any>>(new Map());

  const validFiles = useMemo(
    () => uploadFiles.filter((file) => file.status === "valid"),
    [uploadFiles]
  );

  const uploadSuccessFiles = useMemo(
    () => uploadFiles.filter((file) => file.status === "done"),
    [uploadFiles]
  );

  const inValidFiles = useMemo(
    () => uploadFiles.filter((file) => file.status === "error"),
    [uploadFiles]
  );

  const onBindingChange = () => {
    if (stagingFiles) {
      checkValidFile(stagingFiles);
    }
  };

  const extractDrillHoleProperty = (fileName: string) => {
    const splitFileName = fileName.match(FILE_NAME_REGEX);
    return {
      name: splitFileName?.[1],
      boxDepthFrom: parseFloat(splitFileName?.[2] ?? "0"),
      boxDepthTo: parseFloat(splitFileName?.[4] ?? "0"),
    };
  };

  const uploadImage = async (
    file: any,
    data: FormUploadFiles,
    fileIndex: number
  ) => {
    if (!uploadFiles[fileIndex]) return;
    const drillholeName = watch("isSetDrillHoleName")
      ? watch("setDrillHoleName")
      : extractDrillHoleProperty(file.name).name;

    let drillHoleId =
      createdDrillholeRef.current.get(drillholeName as string)?.id ??
      createdDrillholeRef.current.get(drillholeName as string)?.Id;

    if (!drillHoleId && !watch("isUnnamed")) {
      toast.error(`Cannot find drillhole id for ${drillholeName}`);
      return;
    }
    const response = await imageRequest.uploadImageToProject({
      file,
      drillHoleId,
      projectId: data.projectId,
      prospectId: data.prospectId,
      boundingBoxId: data?.boundingBoxId,
      boundingRowsId: data?.boundingRowId,
      workflowId: data.workflowId,
      isUnnamed: data.isUnnamed,
      imageCategory: data.imageCategory,
      imageTypeId: data.imageTypeId,
      imageSubtypeId: data.imageSubtypeId,
    });
    if (response.state === RequestState.success) {
      setUploadFiles((previousFiles) => {
        const updatedItems = [...previousFiles];
        updatedItems[fileIndex].status = "done";
        updatedItems[fileIndex].reLoading = false;
        return updatedItems;
      });
    }
    if (response.state === RequestState.error) {
      setUploadFiles((previousFiles) => {
        const updatedItems = [...previousFiles];
        updatedItems[fileIndex].status = "error";
        updatedItems[fileIndex].errorMessage = response.message;
        updatedItems[fileIndex].reLoading = false;
        return updatedItems;
      });
    }
  };

  const handleCreateDrillhole = async (data: FormUploadFiles) => {
    let maxDepth = 0;
    let uniqueDHNames: any[] = [];

    if (watch("isSetDrillHoleName")) {
      uniqueDHNames = [data?.setDrillHoleName];
    } else {
      const dHNames = validFiles
        .map((file) => {
          const property = extractDrillHoleProperty(file?.file?.name ?? "");
          maxDepth = Math.max(Number(property.boxDepthTo), maxDepth);
          return property?.name;
        })
        .filter(Boolean);

      uniqueDHNames = Array.from(new Set(dHNames));
    }

    const drillHole = new Map<string, any>();
    for (let i = 0; i < uniqueDHNames.length; i++) {
      if (!uniqueDHNames[i]) continue;
      const response = await drillHoleRequest.create({
        projectId: Number(data.projectId),
        name: uniqueDHNames[i] ?? "",
        drillHoleStatus: DrillHoleStatus.NotStarted,
        prospectId: data?.prospectId,
        rl: 0,
        maxDepth: maxDepth,
      });
      if (
        response.state === RequestState.error &&
        !isEmpty(response?.details)
      ) {
        drillHole.set(uniqueDHNames[i] ?? "", JSON.parse(response?.details));
      }

      if (
        response.state === RequestState.success &&
        !drillHole.has(uniqueDHNames[i] ?? "")
      ) {
        drillHole.set(uniqueDHNames[i] ?? "", response.data);
      }
    }
    return drillHole;
  };
  useEffect(() => {
    if (globalProjectId) {
      setValue("projectId", globalProjectId);
      setValue("prospectId", undefined);
    }
    if (globalProspectId) {
      setValue("prospectId", globalProspectId);
    }
  }, [globalProjectId, globalProspectId]);

  const onSubmit: SubmitHandler<FormUploadFiles> = async (data) => {
    // Check if select ai naming, must select a workflow that includes AI Image Naming
    if (watch("isAiNaming") && data.workflowId) {
      const hasNamingStep =
        await stepWorkflowsRequest.validateAiNamingInWorkflow(data.workflowId);
      if (hasNamingStep.state === RequestState.error) {
        toast.error("Please select a workflow that includes AI Image Naming");
        return;
      }
    }
    setIsUploading(true);
    // Create drillhole first
    createdDrillholeRef.current = await handleCreateDrillhole(data);

    if (isEmpty(createdDrillholeRef.current) && !watch("isUnnamed")) {
      toast.error("Please specific the drillhole name");
      setIsUploading(false);
      return;
    }
    // Upload images
    const requestPerSend = 2;
    const totalRequest = Math.ceil(validFiles.length / requestPerSend);

    for (let index = 0; index < totalRequest; index++) {
      const startIndex = index * requestPerSend;
      const endIndex = Math.min(startIndex + requestPerSend, validFiles.length);

      const uploadFiles = validFiles.slice(startIndex, endIndex);
      const uploadPromises = uploadFiles.map((file, uploadIndex) =>
        uploadImage(
          file.file,
          data,
          index * requestPerSend + uploadIndex + inValidFiles.length
        )
      );
      await Promise.all(uploadPromises);
    }
    setIsUploading(false);
  };
  const getBorderColor = useCallback((status: string) => {
    if (status === "valid") return "border-gray-300";
    if (status === "done") return "border-green-500";
    if (status === "error") return "border-red-500";
    return "border-gray-300";
  }, []);

  const getTextColor = useCallback((status: string) => {
    if (status === "valid") return "text-black";
    if (status === "done") return "text-green-500";
    if (status === "error") return "text-red-500";
    return "text-black";
  }, []);

  const checkValidFile = async (files: any, only: boolean = false) => {
    setLoading(true);
    const valids: UploadFiles[] = [];
    const inValids: UploadFiles[] = [];
    const fileName = Array.from(files as FileList).map(
      (file: File) => file.name
    );

    const filesExists = await imageRequest.validateImages({
      names: fileName,
      projectId: watch("projectId"),
    });
    for (let index = 0; index < files.length; index++) {
      const element = files[index];
      if (watch("isUnnamed") || watch("isSetDrillHoleName")) {
        valids.push({
          file: element,
          url: URL.createObjectURL(element),
          status: "valid",
          reLoading: false,
        });
        continue;
      }
      if (!FILE_NAME_REGEX.test(element.name)) {
        inValids.push({
          file: element,
          url: URL.createObjectURL(element),
          status: "error",
          errorMessage:
            "File name must be in the format:'{HOLEID}_{BoxDepthFrom}_{BoxDepthTo}'",
          errorType: "errorName",
          reLoading: false,
        });
        continue;
      }
      if ((filesExists.data ?? []).includes(element.name)) {
        inValids.push({
          file: element,
          url: URL.createObjectURL(element),
          status: "error",
          errorMessage: `The file '${element.name}' already exists.`,
          errorType: "errorName",
          reLoading: false,
        });
        continue;
      }
      valids.push({
        file: element,
        url: URL.createObjectURL(element),
        status: "valid",
        reLoading: false,
      });
    }
    setUploadFiles(
      only ? [...inValids, ...uploadFiles, ...valids] : [...inValids, ...valids]
    );
    setLoading(false);
  };

  useEffect(() => {
    stagingFiles?.length && checkValidFile(stagingFiles);
  }, [watch("isUnnamed")]);

  useEffect(() => {
    if (workflows?.length <= maxResultCountWorkflow) {
      setEndWorkflow(true);
    } else {
      setEndWorkflow(false);
    }
  }, [maxResultCountWorkflow, workflows]);
  useEffect(() => {
    requestGetListWorkflows({
      maxResultCount: maxResultCountWorkflow,
      skipCount: 0,
      isActive: true,
      sortField: "name",
      sortOrder: "asc",
    });
  }, [maxResultCountWorkflow]);
  const handleScrollWorkflow = (event: any) => {
    const target = event.target;
    if (
      target.scrollTop + target.offsetHeight === target.scrollHeight &&
      !isEndWorkflow
    ) {
      setMaxResultCountWorkflow(maxResultCountWorkflow + 10);
    }
  };

  const rowRenderer = useCallback(
    ({ key, index, style }: { key: string; index: number; style: any }) => {
      const renderFile = uploadFiles[index];
      return (
        <div key={key} style={style}>
          <div
            className={`px-1 py-1 relative border rounded-sm w-full flex items-center gap-2 mb-2 ${getBorderColor(
              renderFile.status as string
            )}`}
          >
            {renderFile?.status === "error" && (
              <VscError color="red" size={14} />
            )}
            {renderFile?.status === "done" && (
              <MdCheckCircleOutline color="green" size={14} />
            )}
            {renderFile?.reLoading && <AppLoading className={"text-white"} />}

            <div
              style={{
                backgroundImage: `url(${renderFile.url})`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                width: "55px",
                height: "55px",
                cursor: "pointer",
              }}
              className={`p-1 border ${getBorderColor(
                renderFile.status as string
              )}`}
              onClick={() => setPreviewImageIndex(index)}
            ></div>

            <div className="flex flex-col flex-1 w-3">
              <div className={getTextColor(renderFile.status as string)}>
                {renderFile.file.name} (
                {(renderFile.file.size / 1024 / 1024).toFixed(2)} MB)
              </div>
              {renderFile?.status === "error" && (
                <div className="text-red-500 truncate">
                  <Popover content={renderFile?.errorMessage}>
                    Error: {renderFile?.errorMessage}
                  </Popover>
                </div>
              )}
            </div>

            {renderFile?.status === "valid" && (
              <div className="flex flex-row items-center">
                <Tooltip title="Preview bounding polygon" placement="left">
                  <div
                    className="cursor-pointer mr-2"
                    onClick={() => setPreviewPolygonUrl(renderFile.url)}
                  >
                    <LuView style={{ fontSize: "16px", color: "#635658" }} />
                  </div>
                </Tooltip>

                <div
                  className="cursor-pointer"
                  onClick={() => {
                    setUploadFiles(uploadFiles.filter((_, i) => i !== index));
                    uploadFolderRef.current.value = "";
                  }}
                >
                  <DeleteOutlined
                    style={{ fontSize: "16px", color: "#635658" }}
                  />
                </div>
              </div>
            )}
            {renderFile?.status === "error" && (
              <div className="flex flex-row items-start">
                {renderFile?.errorType === "errorName" && (
                  <div className="flex flex-row">
                    <div
                      className="cursor-pointer"
                      onClick={() => {
                        setUploadFiles(
                          uploadFiles.filter((_, i) => i !== index)
                        );
                        uploadFolderRef.current.value = "";
                      }}
                    >
                      <DeleteOutlined
                        style={{ fontSize: "16px", color: "#DB5E72" }}
                      />
                    </div>
                    <div
                      className="cursor-pointer ml-2"
                      onClick={() => {
                        setFileChange(index);
                        setModalState(true);
                      }}
                    >
                      <EditOutlined
                        style={{ fontSize: "16px", color: "#DB5E72" }}
                      />
                    </div>
                  </div>
                )}

                {renderFile?.errorType !== "errorName" && (
                  <div
                    className="cursor-pointer ml-3"
                    onClick={async () => {
                      setUploadFiles(
                        uploadFiles.map((item, i) =>
                          i === index
                            ? { ...renderFile, reLoading: true }
                            : item
                        )
                      );
                      await uploadImage(
                        renderFile.file,
                        {
                          projectId: watch("projectId"),
                          boundingBoxId: watch("boundingBoxId"),
                          boundingRowId: watch("boundingRowId"),
                        },
                        index
                      );
                    }}
                  >
                    {!renderFile?.reLoading && (
                      <ReloadOutlined
                        style={{ fontSize: "16px", color: "#DB5E72" }}
                      />
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      );
    },
    [uploadFiles]
  );

  const getDetailProjectAsync = async (watchSelectedProject: any) => {
    const response = await projectRequest.getDetailProject(
      watchSelectedProject
    );
    if (response.state === RequestState.success) {
      const { data } = response;
      setBoundingBoxes(
        data?.boundingBoxs?.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
            coordinates: JSON.parse(item?.coordinates ?? "[]").map(
              (coordinate) => ({
                ...coordinate,
                type: item.type === 1 ? "box" : "row",
              })
            ),
          };
        })
      );
      setBoundingRows(
        data?.boundingRows?.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
            coordinates: JSON.parse(item?.coordinates ?? "[]").map(
              (coordinate) => ({
                ...coordinate,
                type: item.type === 1 ? "box" : "row",
              })
            ),
          };
        })
      );

      if (!(data?.boundingBoxs[0]?.id && data?.boundingRows[0]?.id)) return;
      reset(
        {
          boundingBoxId: data?.boundingBoxs[0]?.id,
          boundingRowId: data?.boundingRows[0]?.id,
        },
        { keepValues: true }
      );
    }
  };

  useEffect(() => {
    if (projectSelected) {
      getDetailProjectAsync(projectSelected);
    }
  }, [projectSelected]);

  useEffect(() => {
    if (uploadFolderRef.current !== null && uploadType === "folder") {
      uploadFolderRef.current.setAttribute("directory", "");
      uploadFolderRef.current.setAttribute("webkitdirectory", "");
    }

    if (uploadFolderRef.current !== null && uploadType === "files") {
      uploadFolderRef.current.removeAttribute("directory");
      uploadFolderRef.current.removeAttribute("webkitdirectory");
    }
  }, [uploadFolderRef, uploadType]);

  useEffect(() => {
    const confirmLeaveWhileUploading = (event: any) => {
      const message = "Images are uploading. Are you sure you want to leave?";
      event.returnValue = message;
      return message;
    };
    if (isUploading) {
      window.addEventListener("beforeunload", confirmLeaveWhileUploading);
    } else {
      window.removeEventListener("beforeunload", confirmLeaveWhileUploading);
    }

    return () => {
      window.removeEventListener("beforeunload", confirmLeaveWhileUploading);
    };
  }, [isUploading]);

  const {
    data: imageTypeData,
    setSearchParams: setSearchParamsImageType,
    searchParams: imageTypeSearchParams,
  } = useQueryImageType();
  const {
    data: imageSubTypeData,
    setSearchParams: setSearchParamsImageSubType,
    searchParams: imageSubTypeSearchParams,
  } = useQueryImageSubType();

  useEffect(() => {
    if (globalProjectId) {
      setSearchParamsImageType((prev) => ({
        ...prev,
        projectId: globalProjectId,
      }));
    }
  }, [globalProjectId, setSearchParamsImageType]);
  const imageTypeOptions = imageTypeData?.data?.items?.map((item: any) => {
    return {
      label: item.name,
      value: item.id,
    };
  });
  const imageSubTypeOptions = imageSubTypeData?.data?.items?.map(
    (item: any) => {
      return {
        label: item.name,
        value: item.id,
      };
    }
  );
  const imageTypeId = watch("imageTypeId");

  // Update validation schema with current subtype options for conditional validation
  // This enables dynamic validation: required when options exist, optional when empty
  useEffect(() => {
    setValue("_imageSubtypeOptions", imageSubTypeOptions || []);
  }, [imageSubTypeOptions, setValue]);

  useEffect(() => {
    if (imageTypeId) {
      setSearchParamsImageSubType({
        imageTypeId: imageTypeId,
      });
      // Clear imageSubtypeId when imageTypeId changes to force new selection
      setValue("imageSubtypeId", undefined);
    } else {
      // Clear subtype options and selection when no image type is selected
      setValue("imageSubtypeId", undefined);
      setValue("_imageSubtypeOptions", []);
    }
  }, [imageTypeId, setSearchParamsImageSubType, setValue]);

  return (
    <div className="flex flex-col gap-5">
      <ModalCommon
        open={modalState}
        centered
        padding={0}
        footer={null}
        onCancel={() => {
          reset();
          setModalState(false);
        }}
        style={{ borderRadius: 8 }}
        width={450}
        closable={false}
      >
        <RenameImage
          checkValidFile={checkValidFile}
          fileChange={fileChange}
          setUploadFiles={setUploadFiles}
          uploadFiles={uploadFiles}
          onCancel={() => {
            setModalState(false);
          }}
        />
      </ModalCommon>

      <ModalCommon
        open={!isEmpty(previewImagePolygonUrl)}
        centered
        padding={0}
        footer={null}
        onCancel={() => setPreviewPolygonUrl(undefined)}
        style={{ borderRadius: 8 }}
        width={948}
        closable={true}
      >
        <PreviewPolygon
          image={previewImagePolygonUrl ?? ""}
          selectRowPoygonId={Number(watch("boundingRowId"))}
          selectBoxPoygonId={Number(watch("boundingBoxId"))}
          boxOptions={boundingBoxes}
          rowOptions={boundingRows}
        />
      </ModalCommon>
      <p className="text-34-34 font-semibold">Load Images</p>
      <hr />
      <div className="grid grid-cols-12 gap-5">
        <Form
          onFinish={handleSubmit(onSubmit)}
          className="col-span-6 flex flex-col gap-3"
        >
          <div className="grid grid-cols-2 gap-2">
            <div className="flex flex-col gap-2">
              <Controller
                name="isAiNaming"
                control={control}
                render={({ field }) => (
                  <Checkbox {...field}>
                    <div className="flex items-center">
                      <span className="mr-1">AI image naming</span>
                      <AppGuide hint="If you enable this option, you must specify a workflow that includes AI Image Naming" />
                    </div>
                  </Checkbox>
                )}
              />
            </div>
            <div className="flex flex-col gap-2">
              <Controller
                name="isSetDrillHoleName"
                control={control}
                render={({ field }) => (
                  <Checkbox
                    {...field}
                    onChange={(e) => {
                      onBindingChange();
                      field.onChange(e.target.checked);
                    }}
                  >
                    <div className="flex items-center">
                      <span className="mr-1">Set drillhole name</span>
                      <AppGuide hint="When you upload an image, it will be associated with this drillhole" />
                    </div>
                  </Checkbox>
                )}
              />
              {watch("isSetDrillHoleName") && (
                <Controller
                  name="setDrillHoleName"
                  control={control}
                  render={({ field }) => (
                    <Input
                      placeholder="Please enter the specific drillhole name"
                      {...field}
                    ></Input>
                  )}
                />
              )}
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <p className="font-medium">Workflows</p>
            <Controller
              name="workflowId"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  onSearch={(value) => {
                    requestGetListWorkflows({
                      maxResultCount: 100,
                      skipCount: 0,
                      keyword: value,
                      isActive: true,
                    });
                  }}
                  options={workflows.map((workflow) => {
                    return {
                      label: workflow.name,
                      value: workflow.id,
                    };
                  })}
                  filterOption={false}
                  showSearch
                  placeholder="Select workflow"
                  onPopupScroll={handleScrollWorkflow}
                  allowClear
                />
              )}
            />
          </div>
          <div className="flex gap-2 flex-col">
            {boundingBoxes?.length > 0 && (
              <SelectCommon
                name="boundingBoxId"
                options={boundingBoxes}
                control={control}
                label="Bounding box"
                placeholder="Select bounding box for image"
                allowClear
              />
            )}

            {boundingRows?.length > 0 && (
              <SelectCommon
                name="boundingRowId"
                options={boundingRows}
                control={control}
                label="Bounding row"
                placeholder="Select bounding row for image"
                allowClear
              />
            )}
          </div>
          <SelectCommon
            name="imageCategory"
            options={imageCategoryOptions}
            control={control}
            label="Image Category"
            placeholder="Select Image category"
            isRequired={true}
          />

          <div className="grid grid-cols-3 gap-2">
            <SelectCommon
              name="imageTypeId"
              options={imageTypeOptions}
              control={control}
              label="Image Type"
              placeholder="Select image type"
              isRequired={true}
            />
            {imageSubTypeOptions?.length > 0 && watch("imageTypeId") && (
              <SelectCommon
                name="imageSubtypeId"
                options={imageSubTypeOptions}
                control={control}
                label="Image Subtype"
                placeholder="Select standard type"
                isRequired={imageSubTypeOptions?.length > 0}
              />
            )}
          </div>

          <div className="flex justify-between items-center">
            <div className="flex gap-3">
              <p className="font-medium">Upload Unnamed</p>
              <Controller
                name="isUnnamed"
                control={control}
                render={({ field }) => (
                  <Switch disabled={isUploading} {...field} />
                )}
              />
            </div>
            <div className="flex gap-3">
              <p className="font-medium">Upload type</p>
              <Switch
                value={uploadType === "folder"}
                disabled={isUploading}
                onChange={(value) => {
                  if (value) setUploadType("folder");
                  else setUploadType("files");
                }}
                checkedChildren="Folder"
                unCheckedChildren="Files"
                style={{ width: 80 }}
              />
            </div>
            <div className="flex gap-3 flex-col">
              <Button
                icon={<UploadOutlined />}
                style={{
                  width: 170,
                }}
                disabled={isUploading}
                onClick={() =>
                  uploadFolderRef.current && uploadFolderRef?.current?.click()
                }
              >
                Select {uploadType === "files" ? "files" : "folder"}
              </Button>
              <div className="hidden">
                <input
                  ref={uploadFolderRef}
                  type="file"
                  multiple
                  accept={uploadType === "files" ? "image/*" : ""}
                  name="images"
                  className="w-full text-gray-500 font-semibold text-sm bg-white border file:cursor-pointer cursor-pointer file:border-0 file:py-2 file:px-3 file:mr-4 file:bg-gray-200 file:hover:bg-gray-200 file:text-gray-500 rounded"
                  onChange={async (e) => {
                    const files = e.target.files ?? [];
                    setStagingFiles(files as any);
                    if (files.length) {
                      checkValidFile(files);
                    }
                  }}
                />
              </div>
            </div>
          </div>

          <div className="flex justify-center">
            <ButtonCommon
              loading={isUploading || loading}
              // onClick={upload}
              disabled={validFiles.length === 0}
              type="submit"
              className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none disabled:bg-gray-200 disabled:text-gray-600 disabled:cursor-not-allowed disabled:opacity-50"
            >
              Upload
            </ButtonCommon>
          </div>
        </Form>
        <div className="flex flex-col col-span-6 gap-5">
          <div className="flex flex-col gap-2">
            <ProgressBar
              completed={uploadSuccessFiles.length}
              errors={inValidFiles.length}
              total={uploadFiles.length}
            />
          </div>
          <div className="flex items-center">
            <VituralizeList
              height={500}
              fileList={uploadFiles}
              rowHeight={70}
              rowRenderer={rowRenderer}
            />
          </div>
          {uploadFiles.length > 0 && (
            <div className="italic flex items-center">
              <MdLightbulbOutline />
              <span className="font-medium mr-1">Tips: </span> You can click
              image to prevew.
            </div>
          )}
        </div>
      </div>
      <div className="hidden">
        {uploadFiles.length > 0 && (
          <Image.PreviewGroup
            preview={{
              visible: previewImageIndex !== -1,
              onVisibleChange: (visible, prevVisible) => {
                if (!visible) setPreviewImageIndex(-1);
              },
              current: previewImageIndex,
              onChange: (current, prevCurrent) => {
                setPreviewImageIndex(current);
              },
            }}
          >
            {uploadFiles.map((file, index) => {
              return <Image key={index} src={file.url} />;
            })}
          </Image.PreviewGroup>
        )}
      </div>
    </div>
  );
};
export default UploadImages;
