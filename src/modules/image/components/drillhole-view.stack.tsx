"use client";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { MultiLoggingInfoInterface } from "@/modules/logging/interface/logging.interface";
import { isEmpty, uniqueId } from "lodash";
import { useMemo } from "react";
import {
  reCalculateImageSize,
  resizePointData,
} from "../helpers/image.helpers";
import {
  DrillHoleAttributePoints,
  DrillHoleViewStack,
} from "../model/entities/drillhole.config";
import {
  EnumDrillholeView,
  EnumDrillHoleViewStack,
} from "../model/enum/drillhole.enum";
import { selectDHViewConfig, updateDHViewInfo } from "../redux/imageSlice";
import ImageColumn from "./canvas/image.column";
import PointDataColumn from "./canvas/point-data.column";
import IntervalGeologyColumn from "./canvas/point-data.geology";
import CombinedResultColumn from "./canvas/combined-result.geology";
import { DrillHoleImages } from "./drillhole-view";
import { getCombinedResultText } from "../helpers/point-data.helper";

interface Props {
  dHImages: DrillHoleImages[];
  viewMode: EnumDrillholeView;
  dhAttributePoints: DrillHoleAttributePoints[];
  dhSuitesLogging: MultiLoggingInfoInterface[];
  selectedHoleNames: string[];
}

function DrillHoleViewStacks({
  dHImages,
  viewMode,
  dhAttributePoints,
  dhSuitesLogging,
  selectedHoleNames,
}: Props) {
  const dHViewConfigs = useAppSelector(selectDHViewConfig);
  const showCombinedResultColumn = useAppSelector(
    (state) => state.images.showCombinedResultColumn
  );
  const isCombinedResultTextWide = useAppSelector(
    (state) => state.images.isTextWide
  );

  const { selectedGeologyFieldId } = useAppSelector((state) => state.images);

  const dispatch = useAppDispatch();
  const drillHoleStacks: DrillHoleViewStack[] | undefined = useMemo(() => {
    let dHStacks: DrillHoleViewStack[] = [];
    // Calculate the minimum and maximum height of the images for ruler
    let minHeight = Infinity;
    let maxHeight = -Infinity;
    for (let i = 0; i < selectedHoleNames.length; i++) {
      // Build stack for each selected hole
      const holeName = selectedHoleNames[i];
      const holeImages = dHImages.filter((image) => {
        return image.drillHole.name === holeName;
      });

      const imageStacks = holeImages.map((image, imageIndex) => {
        const imageInfo = reCalculateImageSize(image, viewMode);

        const stack: DrillHoleViewStack = {
          id: uniqueId(),
          type: EnumDrillHoleViewStack.Image,
          data: imageInfo.list,
          index: imageIndex,
          maxWidth: imageInfo.maxWidth,
          minHeight: imageInfo.minHeight,
          maxHeight: imageInfo.maxHeight,
          startX: dHViewConfigs.startX - 2,
          drillhole: image.drillHole,
          imageType: image.type,
          imageSubType: image.subType,
          typeName: image.typeName,
          subTypeName: image.subTypeName,
          uiProps: {
            rotation: imageInfo.rotation,
          },
        };
        minHeight = Math.min(minHeight, imageInfo.minHeight ?? 0);
        maxHeight = Math.max(maxHeight, imageInfo.maxHeight ?? 0);
        return stack;
      });

      dHStacks.push(...imageStacks);

      // calculate data when dHAttributePoints isn't empty
      let pointStacks: DrillHoleViewStack[] = [];

      // filter points by coordinates
      const filterDhPoints = dhAttributePoints?.filter(
        (point) => point?.coor?.length > 0
      );

      if (filterDhPoints?.length !== 0) {
        pointStacks = (dhAttributePoints ?? [])
          .filter((point) => point.drillHoleName === holeName)
          .map((point, index) => {
            const pointInfo = resizePointData(
              point.coor,
              dHViewConfigs.pointDataWidth
            );
            const stack: DrillHoleViewStack = {
              id: uniqueId(),
              type: EnumDrillHoleViewStack.Point,
              data: pointInfo.data,
              index: index,
              maxWidth: pointInfo.maxWidth,
              minHeight: pointInfo.minHeight,
              maxHeight: pointInfo.maxHeight,
              startX: dHViewConfigs.startX,
              drillhole: holeImages?.[0]?.drillHole,
              extraInfo: point?.type,
            };
            minHeight = Math.min(minHeight, pointInfo.minHeight ?? 0);
            maxHeight = Math.max(maxHeight, pointInfo.maxHeight ?? 0);
            return stack;
          });
        dHStacks.push(...pointStacks);
      }

      // add geology suite to stacks
      let geologySuiteStacks: DrillHoleViewStack[] = [];
      const loggingEntries = dhSuitesLogging.find(
        (logging) => logging.drillHoleId === holeImages?.[0]?.drillHole?.name
      );
      if (
        !isEmpty(dhSuitesLogging) &&
        loggingEntries &&
        loggingEntries?.entries?.length > 0 &&
        selectedGeologyFieldId
      ) {
        const geologyData = loggingEntries.entries;
        const stackMinHeight = Math.min(
          ...geologyData.map((item) => item.depthFrom ?? 0)
        );
        const stackMaxHeight = Math.max(
          ...geologyData.map((item) => item.depthTo ?? 0)
        );
        const pointInfo = {
          id: uniqueId(),
          type: EnumDrillHoleViewStack.Geology,
          data: geologyData,
          index: 0,
          maxWidth: viewMode === EnumDrillholeView.DownHole ? 0.12 : 1,
          minHeight: stackMinHeight,
          maxHeight: stackMaxHeight,
          startX: dHViewConfigs.startX,
          drillhole: holeImages?.[0]?.drillHole,
          extraInfo: loggingEntries.suiteName,
        };
        geologySuiteStacks.push(pointInfo);
        minHeight = Math.min(minHeight, pointInfo.minHeight ?? 0);
        maxHeight = Math.max(maxHeight, pointInfo.maxHeight ?? 0);
      }
      dHStacks.push(...geologySuiteStacks);

      // combined result for geology
      let combinedTextStacks: DrillHoleViewStack[] = [];
      if (
        !isEmpty(dhSuitesLogging) &&
        loggingEntries &&
        loggingEntries?.entries?.length > 0 &&
        showCombinedResultColumn
      ) {
        const geologyDatas = loggingEntries.entries;
        const stackMinHeight = Math.min(
          ...geologyDatas.map((item) => item.depthFrom ?? 0)
        );
        const stackMaxHeight = Math.max(
          ...geologyDatas.map((item) => item.depthTo ?? 0)
        );

        const combinedResult = geologyDatas.map((data) => {
          return {
            combineResultText: getCombinedResultText(data),
            depthFrom: data.depthFrom,
            depthTo: data.depthTo,
            dataEntryValues: data.dataEntryValues,
            drillholeId: data.drillholeId,
            geologySuiteId: data.geologySuiteId,
          };
        });

        const maxWidth = Math.max(
          ...combinedResult.map((item) => item.combineResultText.length)
        );

        const realMaxWidth =
          viewMode === EnumDrillholeView.DownHole
            ? maxWidth * 0.031
            : maxWidth * 0.155;

        const realMaxWidthDownhole =
          realMaxWidth / 5 < 1.2 ? 1.2 : realMaxWidth / 5;
        const entryInfo = {
          id: uniqueId(),
          type: EnumDrillHoleViewStack.GeologyCombinedResult,
          data: combinedResult,
          index: 0,
          maxWidth: isCombinedResultTextWide
            ? realMaxWidth
            : realMaxWidthDownhole,
          minHeight: stackMinHeight,
          maxHeight: stackMaxHeight,
          startX: dHViewConfigs.startX,
          drillhole: holeImages?.[0]?.drillHole,
          extraInfo: loggingEntries.suiteName,
        };
        combinedTextStacks.push(entryInfo);
        minHeight = Math.min(minHeight, entryInfo.minHeight ?? 0);
        maxHeight = Math.max(maxHeight, entryInfo.maxHeight ?? 0);
      }
      dHStacks.push(...combinedTextStacks);
    }

    // recalculate startX
    for (let i = 0; i < dHStacks.length; i++) {
      let startX = 0;
      if (i === 0) {
        startX = dHViewConfigs.startX;
      } else {
        startX =
          (dHStacks?.[i - 1]?.startX ?? 0) +
          (dHStacks[i - 1].maxWidth ?? 0) +
          dHViewConfigs.gap;
      }
      dHStacks[i] = {
        ...dHStacks[i],
        startX: startX - 1,
      };
    }

    if (minHeight === Infinity) minHeight = 0;
    if (maxHeight === -Infinity) maxHeight = 0;
    dispatch(
      updateDHViewInfo({
        minHeight: minHeight,
        maxHeight: maxHeight,
      })
    );

    return dHStacks;
  }, [
    dHViewConfigs,
    dHImages,
    viewMode,
    dhAttributePoints,
    dhSuitesLogging,
    selectedHoleNames,
    isCombinedResultTextWide,
    showCombinedResultColumn,
    selectedGeologyFieldId,
  ]);

  return (drillHoleStacks ?? []).map((calculatedImage) => {
    switch (calculatedImage.type) {
      case EnumDrillHoleViewStack.Image: {
        return (
          <ImageColumn
            key={`${calculatedImage?.type}${calculatedImage?.drillhole?.id}${calculatedImage?.imageType}${calculatedImage?.imageSubType}`}
            images={calculatedImage}
          />
        );
      }
      case EnumDrillHoleViewStack.Point: {
        return (
          <PointDataColumn
            key={calculatedImage?.id}
            dataPoints={calculatedImage}
          />
        );
      }

      case EnumDrillHoleViewStack.Geology: {
        return (
          <IntervalGeologyColumn
            key={calculatedImage?.id}
            intervals={calculatedImage}
          />
        );
      }

      case EnumDrillHoleViewStack.GeologyCombinedResult: {
        return showCombinedResultColumn ? (
          <CombinedResultColumn
            key={calculatedImage?.id}
            intervals={calculatedImage}
          />
        ) : null;
      }
    }
  });
}

export default DrillHoleViewStacks;
